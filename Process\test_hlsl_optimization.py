"""
HLSL优化测试脚本

流程：
1. 读取输入HLSL文件
2. 使用HLSLOptimizerManager进行优化
3. 输出优化后的代码到input.hlsl
4. 调用dxc编译器编译
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加路径以便导入模块
current_dir = Path(__file__).parent
sys.path.append(str(current_dir.parent))

from basic_optimizers import HLSLOptimizerManager, OptimizationLevel

class HLSLOptimizationTester:
    def __init__(self):
        self.manager = HLSLOptimizerManager(OptimizationLevel.AGGRESSIVE)
        self.input_file = "input.hlsl"
        self.output_file = "output.vso"
        
        # 测试用的HLSL代码（包含cbuffer以测试缓存功能）
        self.test_hlsl_code = """
// 测试HLSL着色器 - 包含cbuffer以测试缓存功能
cbuffer Constants : register(b0)
{
    float4x4 worldMatrix;
    float4x4 viewMatrix;
    float4x4 projMatrix;
    float4 lightDirection;
    float4 lightColor;
};

struct VSInput
{
    float3 position : POSITION;
    float3 normal : NORMAL;
    float2 texcoord : TEXCOORD0;
};

struct VSOutput
{
    float4 position : SV_POSITION;
    float3 worldPos : TEXCOORD0;
    float3 normal : TEXCOORD1;
    float2 texcoord : TEXCOORD2;
    float3 lightDir : TEXCOORD3;
};

VSOutput vsmain(VSInput input)
{
    VSOutput output;
    
    // 位置变换 - 可以优化的数学运算，包含重复的cbuffer访问
    float4 worldPos = mul(float4(input.position, 1.0), worldMatrix);
    float4 viewPos = mul(worldPos, viewMatrix);
    output.position = mul(viewPos, projMatrix);

    // 法线变换 - 重复访问worldMatrix
    output.normal = normalize(mul(input.normal, (float3x3)worldMatrix));

    // 纹理坐标
    output.texcoord = input.texcoord;

    // 光照计算 - 包含可优化的数学函数和重复的cbuffer访问
    output.worldPos = worldPos.xyz;
    output.lightDir = normalize(lightDirection.xyz);

    // 重复访问lightDirection和lightColor进行测试
    float3 lightDir2 = lightDirection.xyz;
    float3 lightCol = lightColor.rgb;
    
    // 一些可以优化的数学运算
    float distance = sqrt(dot(worldPos.xyz, worldPos.xyz));
    float attenuation = 1.0 / (1.0 + distance * distance);
    
    // 分支优化测试
    if (attenuation > 0.5)
    {
        output.lightDir *= 1.2;
    }
    else
    {
        output.lightDir *= 0.8;
    }
    
    // 循环优化测试
    for (int i = 0; i < 4; i++)
    {
        output.lightDir += 0.1;
    }
    
    // 幂函数优化测试
    float brightness = pow(attenuation, 2.0) + pow(distance, 3.0);
    output.lightDir *= brightness;
    
    return output;
}
        """.strip()
    
    def read_hlsl_file(self, file_path):
        """读取HLSL文件"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"✓ 成功读取文件: {file_path}")
                print(f"  文件大小: {len(content)} 字符")
                return content
            else:
                print(f"⚠ 文件不存在: {file_path}")
                print("  使用默认测试代码")
                return self.test_hlsl_code
        except Exception as e:
            print(f"✗ 读取文件失败: {e}")
            print("  使用默认测试代码")
            return self.test_hlsl_code
    
    def optimize_hlsl(self, hlsl_code):
        """使用HLSLOptimizerManager优化HLSL代码"""
        print("\n=== 开始HLSL优化 ===")
        
        try:
            # 获取所有可用的优化器
            available_optimizers = self.manager.get_available_optimizers()
            print(f"✓ 可用优化器: {len(available_optimizers)}个")
            print(f"  优化器列表: {available_optimizers}")
            
            # 分析代码特征
            characteristics = self.manager.analyze_code_characteristics(hlsl_code)
            print(f"✓ 代码特征分析完成")
            print(f"  代码行数: {characteristics['line_count']}")
            print(f"  复杂度分数: {characteristics['complexity_score']}")
            print(f"  推荐优化器: {characteristics['recommended_optimizers']}")
            
            # 执行优化（使用所有优化器）
            print(f"\n开始执行优化...")
            optimized_code = self.manager.optimize_all(hlsl_code)
            
            # 获取统计信息
            stats = self.manager.get_total_statistics()
            print(f"\n✓ 优化完成")
            print(f"  应用的优化次数: {stats['total_optimizations_applied']}")
            print(f"  处理的代码行数: {stats['total_lines_processed']}")
            print(f"  使用的优化器数量: {stats['optimizers_used']}")
            
            # 显示详细统计
            if stats['individual_stats']:
                print(f"\n详细统计信息:")
                for optimizer_name, optimizer_stats in stats['individual_stats'].items():
                    if 'optimizations_applied' in optimizer_stats:
                        print(f"  {optimizer_name}: {optimizer_stats['optimizations_applied']}次优化")
            
            return optimized_code
            
        except Exception as e:
            print(f"✗ 优化失败: {e}")
            import traceback
            traceback.print_exc()
            return hlsl_code
    
    def write_optimized_code(self, optimized_code):
        """将优化后的代码写入input.hlsl文件"""
        try:
            with open(self.input_file, 'w', encoding='utf-8') as f:
                f.write(optimized_code)
            print(f"\n✓ 优化后的代码已写入: {self.input_file}")
            print(f"  文件大小: {len(optimized_code)} 字符")
            return True
        except Exception as e:
            print(f"✗ 写入文件失败: {e}")
            return False
    
    def compile_with_dxc(self):
        """使用dxc编译器编译HLSL"""
        print(f"\n=== 开始DXC编译 ===")
        
        # 构建dxc命令
        dxc_command = [
            "dxc",
            "-T", "ps_5_0",      # 目标着色器模型：像素着色器5.0
            "-E", "main",        # 入口点函数名
            "-Fo", self.output_file,  # 输出文件
            self.input_file      # 输入文件
        ]
        
        try:
            print(f"执行命令: {' '.join(dxc_command)}")
            
            # 执行编译
            result = subprocess.run(
                dxc_command,
                capture_output=True,
                text=True,
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                print(f"✓ 编译成功!")
                print(f"  输出文件: {self.output_file}")
                
                # 检查输出文件大小
                if os.path.exists(self.output_file):
                    file_size = os.path.getsize(self.output_file)
                    print(f"  输出文件大小: {file_size} 字节")
                
                if result.stdout:
                    print(f"  编译器输出: {result.stdout}")
                
                return True
            else:
                print(f"✗ 编译失败 (返回码: {result.returncode})")
                if result.stderr:
                    print(f"  错误信息: {result.stderr}")
                if result.stdout:
                    print(f"  输出信息: {result.stdout}")
                return False
                
        except FileNotFoundError:
            print("✗ 找不到dxc编译器")
            print("  请确保dxc.exe在PATH环境变量中，或者在当前目录下")
            return False
        except Exception as e:
            print(f"✗ 编译过程中发生错误: {e}")
            return False
    
    def run_test(self, input_hlsl_file=None):
        """运行完整的测试流程"""
        print("=== HLSL优化测试开始 ===")
        
        # 步骤1: 读取输入HLSL文件
        if input_hlsl_file:
            hlsl_code = self.read_hlsl_file(input_hlsl_file)
        else:
            print("使用默认测试HLSL代码")
            hlsl_code = self.test_hlsl_code

        # 如果读取的文件有问题，使用默认代码
        if "/*$(Variable:" in hlsl_code or "ViewModes::" in hlsl_code:
            print("⚠ 检测到模板变量，使用默认测试代码")
            hlsl_code = self.test_hlsl_code
        
        # 步骤2: 优化HLSL代码
        optimized_code = self.optimize_hlsl(hlsl_code)
        
        # 步骤3: 输出优化后的代码
        if not self.write_optimized_code(optimized_code):
            return False
        
        # 步骤4: 使用dxc编译
        compile_success = self.compile_with_dxc()
        
        print(f"\n=== 测试完成 ===")
        if compile_success:
            print("🎉 所有步骤都成功完成!")
        else:
            print("⚠ 编译步骤失败，但优化步骤成功")
        
        return compile_success

def main():
    """主函数"""
    tester = HLSLOptimizationTester()
    
    # 你可以在这里指定输入文件路径
    # input_file = "path/to/your/shader.hlsl"  # 手动补全文件路径
    input_file = "D:/XuSong/Project/shaderProfile/shader/RGA_Test/TestShaderPS.hlsl"  # 使用默认测试代码
    
    # 运行测试
    success = tester.run_test(input_file)
    
    if success:
        print("\n✓ 测试脚本执行成功")
    else:
        print("\n✗ 测试脚本执行失败")

if __name__ == "__main__":
    main()
