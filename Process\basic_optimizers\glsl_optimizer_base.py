"""
GLSL优化器基类，定义统一的接口和通用功能。
"""
from abc import ABC, abstractmethod
import re
from typing import Dict, List, Set, Tuple, Optional
from enum import Enum

class OptimizationLevel(Enum):
    """优化级别枚举"""
    NONE = 0
    BASIC = 1
    AGGRESSIVE = 2
    MAXIMUM = 3

class GLSLOptimizerBase(ABC):
    """
    GLSL优化器基类，所有GLSL优化器都应该继承此类。
    
    提供统一的接口和通用的辅助功能。
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        """
        初始化优化器
        
        Args:
            optimization_level: 优化级别
        """
        self.optimization_level = optimization_level
        self.statistics = {
            'optimizations_applied': 0,
            'lines_processed': 0,
            'functions_processed': 0,
            'variables_processed': 0
        }
    
    @abstractmethod
    def optimize_code(self, code: str) -> str:
        """
        优化GLSL代码的抽象方法，子类必须实现

        Args:
            code: 输入的GLSL代码字符串

        Returns:
            优化后的GLSL代码字符串
        """
        pass
    
    def get_statistics(self) -> Dict:
        """获取优化统计信息"""
        return self.statistics.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        for key in self.statistics:
            self.statistics[key] = 0
    
    # 通用的辅助方法
    def extract_functions(self, code: str) -> List[Tuple[str, str, int, int]]:
        """
        提取代码中的函数定义
        
        Returns:
            List of (function_name, function_body, start_line, end_line)
        """
        functions = []
        lines = code.splitlines()
        i = 0
        
        # GLSL函数定义正则表达式
        func_pattern = re.compile(r'^\s*(?:highp|mediump|lowp)?\s*([a-zA-Z0-9_]+)\s+([a-zA-Z0-9_]+)\s*\([^)]*\)\s*{?')
        
        while i < len(lines):
            line = lines[i]
            match = func_pattern.match(line)
            if match:
                func_name = match.group(2)
                start_line = i
                
                # 找到函数体结束位置
                brace_count = line.count('{') - line.count('}')
                if brace_count == 0 and '{' in line:
                    brace_count = 1
                
                func_lines = [line]
                i += 1
                
                while i < len(lines) and brace_count > 0:
                    line = lines[i]
                    func_lines.append(line)
                    brace_count += line.count('{') - line.count('}')
                    i += 1
                
                end_line = i - 1
                func_body = '\n'.join(func_lines)
                functions.append((func_name, func_body, start_line, end_line))
            else:
                i += 1
        
        return functions
    
    def extract_variables(self, code: str) -> Set[str]:
        """提取代码中声明的变量名"""
        variables = set()
        # GLSL变量声明模式，包括精度限定符
        var_pattern = re.compile(r'\b(?:highp|mediump|lowp)?\s*(?:float|int|uint|bool|vec[234]|ivec[234]|uvec[234]|mat[234](?:x[234])?|sampler2D|samplerCube)(?:\[\d*\])?\s+([a-zA-Z_][a-zA-Z0-9_]*)')
        
        for match in var_pattern.finditer(code):
            variables.add(match.group(1))
        
        return variables
    
    def is_shader_entry_point(self, func_name: str) -> bool:
        """判断是否为着色器入口点函数"""
        entry_points = {'main'}  # GLSL只有main作为入口点
        return func_name.lower() in entry_points
    
    def count_instruction_complexity(self, code: str) -> int:
        """估算指令复杂度"""
        complexity = 0
        
        # 数学函数权重
        math_functions = {
            'sin': 8, 'cos': 8, 'tan': 10, 'asin': 12, 'acos': 12, 'atan': 10,
            'exp': 8, 'log': 8, 'pow': 12, 'sqrt': 4, 'inversesqrt': 3,
            'normalize': 6, 'length': 4, 'distance': 5, 'dot': 2, 'cross': 4,
            'reflect': 6, 'refract': 8, 'faceforward': 4
        }
        
        # 纹理采样权重
        texture_ops = {
            'texture': 8, 'textureLod': 10, 'textureGrad': 12, 'textureProj': 10,
            'texelFetch': 4, 'textureGather': 6, 'textureSize': 2
        }
        
        # 计算数学函数复杂度
        for func, weight in math_functions.items():
            complexity += len(re.findall(rf'\b{func}\s*\(', code, re.IGNORECASE)) * weight
        
        # 计算纹理操作复杂度
        for op, weight in texture_ops.items():
            complexity += len(re.findall(rf'\b{op}\s*\(', code, re.IGNORECASE)) * weight
        
        # 基础运算复杂度
        complexity += len(re.findall(r'[+\-*/]', code))
        
        return complexity
    
    def get_glsl_type_size(self, type_name: str) -> int:
        """获取GLSL类型的字节大小"""
        type_sizes = {
            'bool': 4, 'int': 4, 'uint': 4, 'float': 4,
            'vec2': 8, 'vec3': 12, 'vec4': 16,
            'ivec2': 8, 'ivec3': 12, 'ivec4': 16,
            'uvec2': 8, 'uvec3': 12, 'uvec4': 16,
            'bvec2': 8, 'bvec3': 12, 'bvec4': 16,
            'mat2': 16, 'mat3': 36, 'mat4': 64,
            'mat2x2': 16, 'mat2x3': 24, 'mat2x4': 32,
            'mat3x2': 24, 'mat3x3': 36, 'mat3x4': 48,
            'mat4x2': 32, 'mat4x3': 48, 'mat4x4': 64
        }
        return type_sizes.get(type_name, 4)
    
    def get_precision_qualifier(self, var_name: str, code: str) -> Optional[str]:
        """获取变量的精度限定符"""
        pattern = re.compile(rf'\b(highp|mediump|lowp)\s+[a-zA-Z0-9_]+\s+{re.escape(var_name)}\b')
        match = pattern.search(code)
        return match.group(1) if match else None
    
    def is_builtin_variable(self, var_name: str) -> bool:
        """判断是否为GLSL内置变量"""
        builtin_vars = {
            # 顶点着色器内置变量
            'gl_Position', 'gl_PointSize', 'gl_ClipDistance', 'gl_CullDistance',
            'gl_VertexID', 'gl_InstanceID', 'gl_DrawID', 'gl_BaseVertex', 'gl_BaseInstance',
            
            # 片段着色器内置变量
            'gl_FragCoord', 'gl_FrontFacing', 'gl_ClipDistance', 'gl_CullDistance',
            'gl_PointCoord', 'gl_PrimitiveID', 'gl_SampleID', 'gl_SamplePosition',
            'gl_SampleMaskIn', 'gl_Layer', 'gl_ViewportIndex',
            'gl_FragColor', 'gl_FragData', 'gl_FragDepth', 'gl_SampleMask',
            
            # 几何着色器内置变量
            'gl_in', 'gl_InvocationID', 'gl_PrimitiveIDIn',
            
            # 计算着色器内置变量
            'gl_NumWorkGroups', 'gl_WorkGroupSize', 'gl_WorkGroupID',
            'gl_LocalInvocationID', 'gl_GlobalInvocationID', 'gl_LocalInvocationIndex'
        }
        return var_name in builtin_vars
    
    def extract_shader_stage(self, code: str) -> str:
        """从代码中推断着色器阶段"""
        if 'gl_Position' in code or 'gl_VertexID' in code:
            return 'vertex'
        elif 'gl_FragColor' in code or 'gl_FragCoord' in code or 'gl_FragDepth' in code:
            return 'fragment'
        elif 'gl_InvocationID' in code or 'EmitVertex' in code:
            return 'geometry'
        elif 'gl_TessCoord' in code:
            return 'tessellation'
        elif 'gl_NumWorkGroups' in code or 'gl_WorkGroupID' in code:
            return 'compute'
        else:
            return 'unknown'
