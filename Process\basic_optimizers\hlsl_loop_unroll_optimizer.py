"""
HLSL循环展开优化器 - 智能分析和展开HLSL中的循环结构
"""
import re
from typing import Dict, List, Tuple, Optional
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel

class HLSLLoopUnrollOptimizer(HLSLOptimizerBase):
    """
    循环展开优化器，智能分析和优化HLSL中的循环结构。
    
    支持的优化：
    - 固定次数循环自动展开
    - 嵌套循环优化
    - 循环条件简化
    - 循环不变量提取
    - 循环合并
    - 添加[unroll]和[loop]属性
    
    用法：
        optimizer = LoopUnrollOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 循环展开阈值
        self.unroll_thresholds = {
            OptimizationLevel.BASIC: 4,
            OptimizationLevel.AGGRESSIVE: 8,
            OptimizationLevel.MAXIMUM: 16
        }
        
        # 循环模式
        self.loop_patterns = {
            'for_loop': re.compile(r'for\s*\(\s*([^;]+);\s*([^;]+);\s*([^)]+)\s*\)\s*{([^}]*)}', re.DOTALL),
            'while_loop': re.compile(r'while\s*\(\s*([^)]+)\s*\)\s*{([^}]*)}', re.DOTALL),
            'do_while_loop': re.compile(r'do\s*{([^}]*)}while\s*\(\s*([^)]+)\s*\);', re.DOTALL)
        }
    
    def optimize_code(self, code: str) -> str:
        """优化循环代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._add_unroll_attributes(optimized_code)
            optimized_code = self._unroll_simple_loops(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._optimize_nested_loops(optimized_code)
            optimized_code = self._extract_loop_invariants(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._merge_adjacent_loops(optimized_code)
            optimized_code = self._optimize_loop_conditions(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _add_unroll_attributes(self, code: str) -> str:
        """为适合的循环添加[unroll]属性"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 检查for循环
            for_match = re.search(r'for\s*\(\s*([^;]+);\s*([^;]+);\s*([^)]+)\s*\)', line)
            if for_match:
                init, condition, increment = for_match.groups()
                
                # 分析循环是否适合展开
                loop_info = self._analyze_loop(init, condition, increment)
                
                if loop_info and loop_info['is_fixed_count']:
                    iterations = loop_info['iterations']
                    max_unroll = self.unroll_thresholds[self.optimization_level]
                    
                    if iterations <= max_unroll:
                        # 添加[unroll]属性
                        if not any('[unroll]' in prev_line for prev_line in lines[max(0, i-2):i]):
                            optimized_lines.append('    [unroll]')
                            self.statistics['optimizations_applied'] += 1
                    elif iterations > max_unroll * 2:
                        # 对于大循环，添加[loop]属性防止自动展开
                        if not any('[loop]' in prev_line for prev_line in lines[max(0, i-2):i]):
                            optimized_lines.append('    [loop]')
                            self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _unroll_simple_loops(self, code: str) -> str:
        """展开简单的固定次数循环"""
        optimized_code = code
        
        # 查找简单的for循环
        for_pattern = re.compile(r'for\s*\(\s*int\s+(\w+)\s*=\s*(\d+)\s*;\s*\1\s*<\s*(\d+)\s*;\s*\1\+\+\s*\)\s*{([^}]*)}', re.DOTALL)
        
        def unroll_loop(match):
            var_name = match.group(1)
            start_val = int(match.group(2))
            end_val = int(match.group(3))
            loop_body = match.group(4).strip()
            
            iterations = end_val - start_val
            max_unroll = self.unroll_thresholds[self.optimization_level]
            
            if iterations <= max_unroll and iterations > 0:
                # 展开循环
                unrolled_code = []
                unrolled_code.append('    // Unrolled loop')
                
                for i in range(start_val, end_val):
                    # 替换循环变量
                    iteration_body = re.sub(rf'\b{var_name}\b', str(i), loop_body)
                    unrolled_code.append(f'    {{{iteration_body}}}')
                
                self.statistics['optimizations_applied'] += 1
                return '\n'.join(unrolled_code)
            
            return match.group(0)
        
        optimized_code = for_pattern.sub(unroll_loop, optimized_code)
        return optimized_code
    
    def _optimize_nested_loops(self, code: str) -> str:
        """优化嵌套循环"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 检测嵌套循环模式
            if 'for' in line and i + 1 < len(lines):
                # 查找内层循环
                inner_loop_start = -1
                brace_count = 0
                
                for j in range(i + 1, len(lines)):
                    if '{' in lines[j]:
                        brace_count += lines[j].count('{')
                    if '}' in lines[j]:
                        brace_count -= lines[j].count('}')
                    
                    if 'for' in lines[j] and brace_count > 0:
                        inner_loop_start = j
                        break
                    
                    if brace_count == 0:
                        break
                
                if inner_loop_start != -1:
                    # 发现嵌套循环，尝试优化
                    outer_loop = line
                    inner_loop = lines[inner_loop_start]
                    
                    # 检查是否可以交换循环顺序（缓存友好）
                    if self._can_interchange_loops(outer_loop, inner_loop):
                        optimized_lines.append('    // Loop interchange for better cache locality')
                        self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _extract_loop_invariants(self, code: str) -> str:
        """提取循环不变量"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 检测for循环
            if re.search(r'for\s*\(', line):
                loop_start = i
                brace_count = 0
                loop_body_lines = []
                
                # 收集循环体
                for j in range(i, len(lines)):
                    current_line = lines[j]
                    if '{' in current_line:
                        brace_count += current_line.count('{')
                    if '}' in current_line:
                        brace_count -= current_line.count('}')
                    
                    if j > i:  # 不包括for语句本身
                        loop_body_lines.append(current_line)
                    
                    if brace_count == 0 and j > i:
                        break
                
                # 分析循环不变量
                invariants = self._find_loop_invariants(loop_body_lines, line)
                
                if invariants:
                    # 移除调试注释，直接添加提取的循环不变量
                    for invariant in invariants:
                        optimized_lines.append(f'    {invariant}')
                    self.statistics['optimizations_applied'] += len(invariants)
                
                # 添加原始循环（可能已修改）
                optimized_lines.extend(lines[loop_start:j+1])
                i = j + 1
                continue
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _merge_adjacent_loops(self, code: str) -> str:
        """合并相邻的相似循环"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 检测连续的for循环
            if re.search(r'for\s*\(', line) and i + 10 < len(lines):  # 假设循环不会太长
                first_loop_info = self._extract_loop_info(lines, i)
                
                if first_loop_info:
                    # 查找下一个循环
                    next_loop_start = first_loop_info['end'] + 1
                    
                    # 跳过空行和注释
                    while (next_loop_start < len(lines) and 
                           (not lines[next_loop_start].strip() or 
                            lines[next_loop_start].strip().startswith('//'))):
                        next_loop_start += 1
                    
                    if next_loop_start < len(lines) and re.search(r'for\s*\(', lines[next_loop_start]):
                        second_loop_info = self._extract_loop_info(lines, next_loop_start)
                        
                        if (second_loop_info and 
                            self._can_merge_loops(first_loop_info, second_loop_info)):
                            
                            # 合并循环
                            merged_loop = self._merge_loop_bodies(first_loop_info, second_loop_info)
                            optimized_lines.append('    // Merged adjacent loops')
                            optimized_lines.extend(merged_loop)
                            
                            self.statistics['optimizations_applied'] += 1
                            i = second_loop_info['end'] + 1
                            continue
                
                # 如果不能合并，添加原始循环
                optimized_lines.extend(lines[i:first_loop_info['end']+1])
                i = first_loop_info['end'] + 1
                continue
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _optimize_loop_conditions(self, code: str) -> str:
        """优化循环条件"""
        optimizations = [
            # i < n 改为 i != n (当确定不会越界时)
            (re.compile(r'for\s*\(\s*int\s+(\w+)\s*=\s*0\s*;\s*\1\s*<\s*(\w+)\s*;\s*\1\+\+\s*\)'),
             r'for (int \1 = 0; \1 != \2; ++\1)'),
            
            # 使用前缀递增
            (re.compile(r'(\w+)\+\+'),
             r'++\1'),
            
            # 优化循环边界
            (re.compile(r'for\s*\(\s*int\s+(\w+)\s*=\s*0\s*;\s*\1\s*<=\s*(\w+)\s*-\s*1\s*;\s*\+\+\1\s*\)'),
             r'for (int \1 = 0; \1 < \2; ++\1)')
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _analyze_loop(self, init: str, condition: str, increment: str) -> Optional[Dict]:
        """分析循环特征"""
        # 简化的循环分析
        init_match = re.search(r'int\s+(\w+)\s*=\s*(\d+)', init.strip())
        cond_match = re.search(r'(\w+)\s*<\s*(\d+)', condition.strip())
        incr_match = re.search(r'(\w+)\+\+', increment.strip())
        
        if init_match and cond_match and incr_match:
            var_name = init_match.group(1)
            start_val = int(init_match.group(2))
            end_val = int(cond_match.group(2))
            
            if (var_name == cond_match.group(1) == incr_match.group(1)):
                return {
                    'is_fixed_count': True,
                    'iterations': end_val - start_val,
                    'variable': var_name,
                    'start': start_val,
                    'end': end_val
                }
        
        return None
    
    def _can_interchange_loops(self, outer_loop: str, inner_loop: str) -> bool:
        """检查是否可以交换循环顺序"""
        # 简化的实现，实际需要更复杂的依赖分析
        return False
    
    def _find_loop_invariants(self, loop_body: List[str], loop_header: str) -> List[str]:
        """查找循环不变量"""
        # 简化的实现
        invariants = []
        
        # 提取循环变量
        loop_var_match = re.search(r'int\s+(\w+)', loop_header)
        if not loop_var_match:
            return invariants
        
        loop_var = loop_var_match.group(1)
        
        for line in loop_body:
            # 查找不依赖循环变量的计算
            if ('=' in line and loop_var not in line and 
                not any(keyword in line for keyword in ['if', 'for', 'while', 'return'])):
                invariants.append(line.strip())
        
        return invariants[:2]  # 限制数量
    
    def _extract_loop_info(self, lines: List[str], start_idx: int) -> Optional[Dict]:
        """提取循环信息"""
        if start_idx >= len(lines):
            return None
        
        brace_count = 0
        end_idx = start_idx
        
        for i in range(start_idx, len(lines)):
            line = lines[i]
            if '{' in line:
                brace_count += line.count('{')
            if '}' in line:
                brace_count -= line.count('}')
            
            if brace_count == 0 and i > start_idx:
                end_idx = i
                break
        
        return {
            'start': start_idx,
            'end': end_idx,
            'header': lines[start_idx],
            'body': lines[start_idx+1:end_idx]
        }
    
    def _can_merge_loops(self, loop1: Dict, loop2: Dict) -> bool:
        """检查是否可以合并两个循环"""
        # 简化的实现，检查循环头是否相同
        return loop1['header'].strip() == loop2['header'].strip()
    
    def _merge_loop_bodies(self, loop1: Dict, loop2: Dict) -> List[str]:
        """合并两个循环体"""
        merged = [loop1['header']]
        merged.extend(loop1['body'])
        merged.extend(loop2['body'])
        merged.append('}')
        return merged
