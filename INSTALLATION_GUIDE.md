# HLSL Shader Profile 安装指南

本文档详细说明了项目所需的所有依赖库和工具的安装方法。

## 快速安装

### 1. 基础Python依赖

```bash
# 安装基础依赖
pip install -r requirements.txt
```

### 2. 可选依赖安装

根据需要使用的优化器功能，选择性安装以下依赖：

## 详细安装说明

### 核心UI框架 (必需)

#### PyQt5
```bash
pip install PyQt5>=5.15.0
```
- **用途**: 主要的GUI框架
- **相关文件**: 所有UI组件

#### pyqode.core
```bash
pip install pyqode.core>=3.0.0
```
- **用途**: 代码编辑器核心组件
- **相关文件**: `ui/` 文件夹中的编辑器组件

#### QScintilla
```bash
pip install QScintilla>=2.11.0
```
- **用途**: 语法高亮和代码编辑功能
- **相关文件**: 代码编辑器的语法高亮功能

### 网络请求库 (必需)

#### requests
```bash
pip install requests>=2.25.0
```
- **用途**: HTTP请求库，用于Compiler Explorer API调用
- **相关文件**: 
  - `Process/compiler_explorer/compiler_api.py`
  - `ui/compiler_explorer_panel.py`

### 语法分析和编译器集成 (可选)

#### tree-sitter
```bash
pip install tree-sitter>=0.20.0
```
- **用途**: 语法树解析库
- **相关文件**: `Process/tree_sitter_hlsl_optimizer.py`
- **额外要求**: 需要HLSL语法库文件 (见下方说明)

#### clang
```bash
pip install clang>=14.0.0
```
- **用途**: Clang Python绑定，用于C风格HLSL语法分析
- **相关文件**: `Process/clang_hlsl_optimizer.py`
- **注意**: 某些系统可能需要额外安装libclang

### 开发工具 (可选)

#### pytest
```bash
pip install pytest>=6.0.0
```
- **用途**: 单元测试框架
- **使用**: 测试优化器功能

#### black
```bash
pip install black>=22.0.0
```
- **用途**: 代码格式化工具
- **使用**: 开发时保持代码风格一致

## 系统工具依赖

以下工具无法通过pip安装，需要单独下载和配置：

### 1. SPIR-V工具链

**用途**: `Process/spirvtools_hlsl_optimizer.py`

需要安装以下三个工具：

#### glslangValidator
- **功能**: HLSL到SPIR-V编译器
- **下载**: https://github.com/KhronosGroup/glslang/releases
- **安装**: 下载后添加到系统PATH

#### spirv-opt
- **功能**: SPIR-V优化器
- **下载**: https://github.com/KhronosGroup/SPIRV-Tools/releases
- **安装**: 下载后添加到系统PATH

#### spirv-cross
- **功能**: SPIR-V到HLSL反编译器
- **下载**: https://github.com/KhronosGroup/SPIRV-Cross/releases
- **安装**: 下载后添加到系统PATH

**验证安装**:
```bash
glslangValidator --version
spirv-opt --version
spirv-cross --version
```

### 2. Tree-sitter HLSL语法库

**用途**: `Process/tree_sitter_hlsl_optimizer.py`

需要手动编译HLSL语法库：

```bash
# 1. 克隆tree-sitter-hlsl (如果存在)
git clone https://github.com/tree-sitter/tree-sitter-hlsl

# 2. 编译语法库
cd tree-sitter-hlsl
tree-sitter generate
tree-sitter build-wasm

# 3. 生成Python绑定
python -c "
from tree_sitter import Language
Language.build_library('tree-sitter-hlsl.so', ['tree-sitter-hlsl'])
"

# 4. 将.so文件复制到Process目录
cp tree-sitter-hlsl.so /path/to/your/project/Process/
```

**注意**: 如果没有现成的tree-sitter-hlsl语法库，需要自己创建或使用C语法库作为替代。

### 3. libclang (Linux/macOS)

**用途**: `Process/clang_hlsl_optimizer.py`

#### Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install libclang-dev clang
```

#### macOS:
```bash
brew install llvm
```

#### Windows:
下载并安装LLVM: https://releases.llvm.org/

## 平台特定说明

### Windows

1. **Visual Studio Build Tools**: 某些Python包可能需要编译，建议安装Visual Studio Build Tools
2. **PATH配置**: 确保所有工具都添加到系统PATH中
3. **权限**: 某些安装可能需要管理员权限

### Linux

1. **包管理器**: 优先使用系统包管理器安装系统级依赖
2. **开发包**: 安装对应的-dev包以获得头文件

### macOS

1. **Homebrew**: 推荐使用Homebrew安装系统工具
2. **Xcode**: 某些编译可能需要Xcode Command Line Tools

## 验证安装

运行以下脚本验证安装：

```python
# test_dependencies.py
def test_imports():
    try:
        import PyQt5
        print("✓ PyQt5")
    except ImportError:
        print("✗ PyQt5")
    
    try:
        import requests
        print("✓ requests")
    except ImportError:
        print("✗ requests")
    
    try:
        import tree_sitter
        print("✓ tree-sitter")
    except ImportError:
        print("✗ tree-sitter (可选)")
    
    try:
        import clang.cindex
        print("✓ clang")
    except ImportError:
        print("✗ clang (可选)")

if __name__ == "__main__":
    test_imports()
```

## 故障排除

### 常见问题

1. **PyQt5安装失败**
   - 尝试使用conda: `conda install pyqt`
   - 或使用预编译包: `pip install PyQt5-Qt5`

2. **tree-sitter编译失败**
   - 确保安装了C编译器
   - 检查Python开发头文件是否安装

3. **clang导入失败**
   - 检查libclang是否正确安装
   - 设置CLANG_LIBRARY_PATH环境变量

4. **SPIR-V工具找不到**
   - 确保工具已添加到PATH
   - 在Windows上可能需要重启命令行

### 最小化安装

如果只需要基本功能，可以只安装：
```bash
pip install PyQt5 pyqode.core QScintilla requests
```

这将支持：
- 基本UI功能
- Compiler Explorer集成
- 基础的HLSL优化器 (不依赖外部工具的)

## 更新依赖

定期更新依赖以获得最新功能和安全修复：

```bash
pip install --upgrade -r requirements.txt
```
