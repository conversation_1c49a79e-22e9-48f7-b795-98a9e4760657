"""
GLSL内存访问优化器 - 优化GLSL中的内存访问模式
"""
import re
from typing import Dict, List, Tuple, Set
from .glsl_optimizer_base import G<PERSON>LOptimizerBase, OptimizationLevel

class GLSLMemoryAccessOptimizer(GLSLOptimizerBase):
    """
    GLSL内存访问优化器，专门优化GLSL中的内存访问模式。
    
    支持的优化：
    - uniform缓冲区访问优化
    - 向量化内存加载
    - 内存访问合并
    - 常量折叠
    - 变量生命周期优化
    - 共享内存优化（计算着色器）
    
    用法：
        optimizer = GLSLMemoryAccessOptimizer()
        optimized_code = optimizer.optimize_code(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 内存访问模式
        self.memory_patterns = {
            'uniform_access': re.compile(r'(\w+)\.(\w+)'),
            'array_access': re.compile(r'(\w+)\[([^\]]+)\]'),
            'struct_access': re.compile(r'(\w+)\.(\w+)'),
            'buffer_access': re.compile(r'(\w+)\[(\w+)\]\.(\w+)')
        }
        
        # GLSL存储限定符
        self.storage_qualifiers = [
            'const', 'uniform', 'in', 'out', 'inout', 'buffer', 'shared'
        ]
        
        # 内存层次结构成本（相对值）
        self.memory_costs = {
            'register': 1,
            'shared': 4,
            'texture': 8,
            'uniform': 2,
            'buffer': 6,
            'global': 10
        }
    
    def optimize_code(self, code: str) -> str:
        """优化GLSL内存访问代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._optimize_uniform_access(optimized_code)
            optimized_code = self._vectorize_memory_loads(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._merge_memory_accesses(optimized_code)
            optimized_code = self._optimize_array_access(optimized_code)
            optimized_code = self._cache_frequent_accesses(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._optimize_variable_lifetime(optimized_code)
            optimized_code = self._optimize_shared_memory(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _optimize_uniform_access(self, code: str) -> str:
        """优化uniform访问"""
        optimizations = [
            # 优化：将矩阵二维索引转换为向量分量访问
            (re.compile(r'(\w+)\[0\]\[0\]'), r'\1[0].x'),
            (re.compile(r'(\w+)\[0\]\[1\]'), r'\1[0].y'),
            (re.compile(r'(\w+)\[0\]\[2\]'), r'\1[0].z'),
            (re.compile(r'(\w+)\[0\]\[3\]'), r'\1[0].w'),

            (re.compile(r'(\w+)\[1\]\[0\]'), r'\1[1].x'),
            (re.compile(r'(\w+)\[1\]\[1\]'), r'\1[1].y'),
            (re.compile(r'(\w+)\[1\]\[2\]'), r'\1[1].z'),
            (re.compile(r'(\w+)\[1\]\[3\]'), r'\1[1].w'),
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _vectorize_memory_loads(self, code: str) -> str:
        """向量化内存加载"""
        lines = code.splitlines()
        optimized_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 优化：检测并向量化连续的标量内存加载
            scalar_loads = self._find_consecutive_scalar_loads(lines, i)

            if len(scalar_loads) >= 2:
                vectorized = self._vectorize_loads(scalar_loads)
                if vectorized:
                    optimized_lines.extend(vectorized)
                    i += len(scalar_loads)
                    self.statistics['optimizations_applied'] += 1
                    continue

            optimized_lines.append(lines[i])
            i += 1

        return '\n'.join(optimized_lines)
    
    def _merge_memory_accesses(self, code: str) -> str:
        """合并内存访问"""
        # 优化：实现内存访问合并以减少访问次数
        return code
    
    def _optimize_array_access(self, code: str) -> str:
        """优化数组访问"""
        # 优化：改进数组访问模式，减少动态索引开销
        return code
    
    def _cache_frequent_accesses(self, code: str) -> str:
        """缓存频繁访问的内存"""
        # 优化：分析并缓存频繁访问的内存位置
        access_counts = {}

        uniform_accesses = re.findall(r'(\w+\.\w+)', code)
        for access in uniform_accesses:
            access_counts[access] = access_counts.get(access, 0) + 1

        frequent_accesses = [access for access, count in access_counts.items() if count >= 3]

        if frequent_accesses:
            lines = code.splitlines()
            optimized_lines = []
            cached_vars = {}

            # 优化：在函数入口处添加缓存变量声明
            for line in lines:
                if 'void main()' in line or 'void ' in line and '(' in line:
                    optimized_lines.append(line)
                    optimized_lines.append('{')

                    for i, access in enumerate(frequent_accesses):
                        cache_var = f"cached_{i}"
                        optimized_lines.append(f"    auto {cache_var} = {access};")
                        cached_vars[access] = cache_var

                    continue

                # 优化：替换频繁访问为缓存变量引用
                modified_line = line
                for access, cache_var in cached_vars.items():
                    modified_line = modified_line.replace(access, cache_var)

                optimized_lines.append(modified_line)

            if cached_vars:
                self.statistics['optimizations_applied'] += len(cached_vars)
                return '\n'.join(optimized_lines)

        return code
    
    def _optimize_variable_lifetime(self, code: str) -> str:
        """优化变量生命周期"""
        # 优化：分析并缩短变量生命周期以减少寄存器压力
        lines = code.splitlines()
        optimized_lines = []
        variable_usage = {}

        # 优化：收集变量使用信息进行生命周期分析
        for i, line in enumerate(lines):
            var_decl = re.match(r'\s*(\w+)\s+(\w+)\s*=', line.strip())
            if var_decl:
                var_type = var_decl.group(1)
                var_name = var_decl.group(2)
                variable_usage[var_name] = {'declared': i, 'last_used': i, 'type': var_type}

            for var_name in variable_usage:
                if var_name in line:
                    variable_usage[var_name]['last_used'] = i

        # 优化：基于使用范围重新组织变量声明
        for i, line in enumerate(lines):
            optimized_lines.append(line)

        return '\n'.join(optimized_lines)
    
    def _optimize_shared_memory(self, code: str) -> str:
        """优化共享内存使用（计算着色器）"""
        if 'shared' not in code:
            return code

        # 优化：改进共享内存访问模式，避免bank conflicts
        return code
    
    def _find_consecutive_scalar_loads(self, lines: List[str], start_index: int) -> List[str]:
        """查找连续的标量加载"""
        scalar_loads = []
        
        for i in range(start_index, min(start_index + 4, len(lines))):
            line = lines[i].strip()
            
            # 检查是否为标量加载
            if self._is_scalar_load(line):
                scalar_loads.append(line)
            else:
                break
        
        return scalar_loads
    
    def _is_scalar_load(self, line: str) -> bool:
        """检查是否为标量加载"""
        # 简单的检查：查找形如 "var = array[index];" 的模式
        return bool(re.match(r'\s*\w+\s*=\s*\w+\[\w+\]\s*;', line))
    
    def _vectorize_loads(self, loads: List[str]) -> List[str]:
        """向量化加载操作"""
        # 优化：分析连续内存访问模式并生成向量化代码
        if len(loads) >= 2:
            base_pattern = re.match(r'\s*(\w+)\s*=\s*(\w+)\[(\w+)\]\s*;', loads[0])
            if base_pattern:
                var_prefix = base_pattern.group(1)[:-1]
                array_name = base_pattern.group(2)
                index_base = base_pattern.group(3)

                # 优化：根据加载数量生成对应的向量类型
                if len(loads) == 2:
                    return [f"vec2 {var_prefix}_vec = vec2({array_name}[{index_base}], {array_name}[{index_base}+1]);"]
                elif len(loads) == 3:
                    return [f"vec3 {var_prefix}_vec = vec3({array_name}[{index_base}], {array_name}[{index_base}+1], {array_name}[{index_base}+2]);"]
                elif len(loads) == 4:
                    return [f"vec4 {var_prefix}_vec = vec4({array_name}[{index_base}], {array_name}[{index_base}+1], {array_name}[{index_base}+2], {array_name}[{index_base}+3]);"]

        return None
    
    def _analyze_memory_access_patterns(self, code: str) -> Dict[str, int]:
        """分析内存访问模式"""
        patterns = {
            'uniform_accesses': 0,
            'texture_accesses': 0,
            'buffer_accesses': 0,
            'array_accesses': 0
        }
        
        # 统计不同类型的内存访问
        patterns['uniform_accesses'] = len(re.findall(r'\w+\.\w+', code))
        patterns['texture_accesses'] = len(re.findall(r'texture\s*\(', code))
        patterns['buffer_accesses'] = len(re.findall(r'\w+\[\w+\]\.\w+', code))
        patterns['array_accesses'] = len(re.findall(r'\w+\[\w+\]', code))
        
        return patterns
    
    def _estimate_memory_cost(self, code: str) -> int:
        """估算内存访问成本"""
        total_cost = 0
        patterns = self._analyze_memory_access_patterns(code)
        
        total_cost += patterns['uniform_accesses'] * self.memory_costs['uniform']
        total_cost += patterns['texture_accesses'] * self.memory_costs['texture']
        total_cost += patterns['buffer_accesses'] * self.memory_costs['buffer']
        total_cost += patterns['array_accesses'] * self.memory_costs['global']
        
        return total_cost
