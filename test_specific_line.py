#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定行的运算过程生成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_specific_line():
    """测试特定行的运算过程"""
    analyzer = SyntaxTreeAnalyzer()

    # 测试问题行
    test_code = "half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));"

    print(f"测试代码: {test_code}")
    print("=" * 50)

    # 先测试函数调用的正则表达式
    import re
    expr = "half(fast::clamp(dot(_8925, _8921), 0.0, 1.0))"
    func_match = re.match(r'([a-zA-Z_][a-zA-Z0-9_]*(?:::[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\((.*)\)$', expr)
    if func_match:
        print(f"函数名: {func_match.group(1)}")
        print(f"参数字符串: '{func_match.group(2)}'")
    else:
        print("函数调用匹配失败")
    print()
    
    # 分析这一行
    result = analyzer.analyze_shader_with_syntax_trees(test_code)

    print(f"结果键: {list(result.keys())}")

    # 查找这一行的分析结果
    if 'line_analyses' in result:
        print(f"找到 {len(result['line_analyses'])} 行分析结果")
        for i, line_info in enumerate(result['line_analyses']):
            line_content = getattr(line_info, 'line_content', 'N/A')
            print(f"第 {i+1} 行: {line_content}")
            if '_8929' in line_content or 'half' in line_content:
                print(f"  原始行: {line_content}")
                print(f"  运算过程数量: {len(getattr(line_info, 'operation_process', []))}")
                print("  运算过程:")
                for j, op in enumerate(getattr(line_info, 'operation_process', []), 1):
                    print(f"    {j}. {op.string}")
                    left_types = getattr(op, 'left_dataType', [])
                    right_types = getattr(op, 'right_dataType', [])
                    left_str = ', '.join([t.value if hasattr(t, 'value') else str(t) for t in left_types]) if left_types else 'unknown'
                    right_str = ', '.join([t.value if hasattr(t, 'value') else str(t) for t in right_types]) if right_types else 'unknown'
                    print(f"       左: {left_str} 右: {right_str}")

                # 打印语法树信息
                syntax_tree = getattr(line_info, 'syntax_tree', None)
                if syntax_tree:
                    print(f"  语法树根节点: {syntax_tree.node_type.value}({syntax_tree.value})")
                    if syntax_tree.children:
                        for k, child in enumerate(syntax_tree.children):
                            print(f"    子节点{k}: {child.node_type.value}({child.value})")
                            if child.children:
                                for l, grandchild in enumerate(child.children):
                                    print(f"      孙节点{l}: {grandchild.node_type.value}({grandchild.value})")
                print()
    else:
        print("没有找到 'line_analyses' 键")

if __name__ == "__main__":
    test_specific_line()
