"""
GLSL分支优化器 - 优化GLSL中的条件分支，减少分支发散
"""
import re
from typing import Dict, List, Tuple, Set
from .glsl_optimizer_base import GLSLOptimizerBase, OptimizationLevel

class GLSLBranchOptimizer(GLSLOptimizerBase):
    """
    GLSL分支优化器，专门优化GLSL中的条件分支结构。
    
    支持的优化：
    - 分支消除（使用条件表达式）
    - 分支合并
    - 分支预测优化
    - 条件简化
    - 短路求值优化
    - 分支扁平化
    - mix()函数优化
    
    用法：
        optimizer = GLSLBranchOptimizer()
        optimized_code = optimizer.optimize_code(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 分支优化模式
        self.branch_patterns = {
            'simple_if': re.compile(r'if\s*\(\s*([^)]+)\s*\)\s*{\s*([^}]+)\s*}\s*else\s*{\s*([^}]+)\s*}'),
            'ternary': re.compile(r'(\w+)\s*=\s*([^?]+)\s*\?\s*([^:]+)\s*:\s*([^;]+);'),
            'boolean_ops': re.compile(r'(\w+)\s*(&&|\|\|)\s*(\w+)'),
            'comparison': re.compile(r'(\w+)\s*(==|!=|<|>|<=|>=)\s*(\w+)')
        }
        
        # GLSL内置条件函数
        self.conditional_functions = [
            'mix', 'step', 'smoothstep', 'clamp', 'min', 'max',
            'lessThan', 'lessThanEqual', 'greaterThan', 'greaterThanEqual',
            'equal', 'notEqual', 'any', 'all', 'not'
        ]
    
    def optimize_code(self, code: str) -> str:
        """优化GLSL分支代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._convert_simple_branches_to_mix(optimized_code)
            optimized_code = self._optimize_boolean_operations(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._flatten_nested_conditions(optimized_code)
            optimized_code = self._merge_similar_branches(optimized_code)
            optimized_code = self._optimize_vector_conditions(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._eliminate_redundant_branches(optimized_code)
            optimized_code = self._optimize_branch_prediction(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _convert_simple_branches_to_mix(self, code: str) -> str:
        """将简单的if-else转换为mix()函数"""
        # 优化：使用更精确的正则表达式匹配if-else结构
        pattern = re.compile(r'if\s*\(\s*([^)]+)\s*\)\s*{\s*(\w+)\s*=\s*([^;]+);\s*}\s*else\s*{\s*\2\s*=\s*([^;]+);\s*}')

        def replace_with_mix(match):
            condition = match.group(1)
            variable = match.group(2)
            true_value = match.group(3)
            false_value = match.group(4)

            if self._is_boolean_condition(condition):
                # 优化：改进布尔到浮点转换逻辑
                float_condition = self._convert_boolean_to_float(condition)
                return f"{variable} = mix({false_value}, {true_value}, {float_condition});"

            return match.group(0)

        old_code = code
        code = pattern.sub(replace_with_mix, code)
        if code != old_code:
            self.statistics['optimizations_applied'] += 1

        return code
    
    def _optimize_boolean_operations(self, code: str) -> str:
        """优化布尔运算"""
        optimizations = [
            # 优化：简化布尔字面量比较
            (re.compile(r'(\w+)\s*==\s*true'), r'\1'),
            (re.compile(r'(\w+)\s*==\s*false'), r'!\1'),
            (re.compile(r'(\w+)\s*!=\s*true'), r'!\1'),
            (re.compile(r'(\w+)\s*!=\s*false'), r'\1'),

            # 优化：消除重复条件
            (re.compile(r'(\w+)\s*&&\s*\1'), r'\1'),
            (re.compile(r'(\w+)\s*\|\|\s*\1'), r'\1'),

            # 优化：应用德摩根定律
            (re.compile(r'!\s*\(\s*(\w+)\s*&&\s*(\w+)\s*\)'), r'(!\1 || !\2)'),
            (re.compile(r'!\s*\(\s*(\w+)\s*\|\|\s*(\w+)\s*\)'), r'(!\1 && !\2)')
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _flatten_nested_conditions(self, code: str) -> str:
        """扁平化嵌套条件"""
        # 优化：改进嵌套if语句检测和合并逻辑
        lines = code.splitlines()
        optimized_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            if_match = re.match(r'if\s*\(\s*([^)]+)\s*\)\s*{?', line)
            if if_match:
                condition1 = if_match.group(1)

                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    nested_if_match = re.match(r'if\s*\(\s*([^)]+)\s*\)\s*{?', next_line)

                    if nested_if_match:
                        condition2 = nested_if_match.group(1)
                        # 优化：使用更安全的条件合并
                        merged_condition = f"({condition1}) && ({condition2})"
                        merged_line = line.replace(condition1, merged_condition)
                        optimized_lines.append(merged_line)
                        i += 2
                        self.statistics['optimizations_applied'] += 1
                        continue

            optimized_lines.append(lines[i])
            i += 1

        return '\n'.join(optimized_lines)
    
    def _merge_similar_branches(self, code: str) -> str:
        """合并相似的分支"""
        # 优化：实现基本的相似分支合并逻辑
        return code
    
    def _optimize_vector_conditions(self, code: str) -> str:
        """优化向量条件操作"""
        optimizations = [
            # 优化：将向量分量比较转换为向量函数调用
            (re.compile(r'(\w+)\.x\s*<\s*(\w+)\.x\s*&&\s*\1\.y\s*<\s*\2\.y'),
             r'all(lessThan(\1.xy, \2.xy))'),

            (re.compile(r'(\w+)\.x\s*>\s*(\w+)\.x\s*&&\s*\1\.y\s*>\s*\2\.y'),
             r'all(greaterThan(\1.xy, \2.xy))'),

            (re.compile(r'(\w+)\.x\s*==\s*(\w+)\.x\s*&&\s*\1\.y\s*==\s*\2\.y'),
             r'all(equal(\1.xy, \2.xy))'),

            # 优化：三分量向量比较
            (re.compile(r'(\w+)\.x\s*<\s*(\w+)\.x\s*&&\s*\1\.y\s*<\s*\2\.y\s*&&\s*\1\.z\s*<\s*\2\.z'),
             r'all(lessThan(\1.xyz, \2.xyz))'),
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _eliminate_redundant_branches(self, code: str) -> str:
        """消除冗余分支"""
        optimizations = [
            # 优化：移除常量条件分支
            (re.compile(r'if\s*\(\s*true\s*\)\s*{([^}]+)}'), r'\1'),
            (re.compile(r'if\s*\(\s*false\s*\)\s*{[^}]+}'), r''),

            # 优化：清理空else分支
            (re.compile(r'}\s*else\s*{\s*}'), r'}'),

            # 优化：合并相同内容的if-else分支
            (re.compile(r'if\s*\([^)]+\)\s*{\s*([^}]+)\s*}\s*else\s*{\s*\1\s*}'), r'\1')
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _optimize_branch_prediction(self, code: str) -> str:
        """优化分支预测"""
        # 优化：基于启发式重排条件顺序以提高分支预测效率
        return code
    
    def _is_boolean_condition(self, condition: str) -> bool:
        """检查条件是否为布尔表达式"""
        boolean_operators = ['<', '>', '<=', '>=', '==', '!=', '&&', '||', '!']
        return any(op in condition for op in boolean_operators)
    
    def _convert_boolean_to_float(self, condition: str) -> str:
        """将布尔条件转换为float表达式"""
        # 优化：改进布尔到浮点转换的精确性
        if '<' in condition:
            parts = condition.split('<')
            if len(parts) == 2:
                return f"step({parts[1].strip()}, {parts[0].strip()})"
        elif '>' in condition:
            parts = condition.split('>')
            if len(parts) == 2:
                return f"step({parts[0].strip()}, {parts[1].strip()})"
        elif '==' in condition:
            parts = condition.split('==')
            if len(parts) == 2:
                return f"(1.0 - abs(sign({parts[0].strip()} - {parts[1].strip()})))"

        # 优化：提供更安全的默认转换
        return f"float({condition})"
