"""
基于tree-sitter的HLSL语法树自动优化器。
依赖：pip install tree_sitter，tree-sitter-hlsl语法库（需手动下载或自定义）。
"""
from tree_sitter import Language, Parser
import os

class TreeSitterHLSLOptimizer:
    """
    用法：
        optimizer = TreeSitterHLSLOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)

    或者带选项：
        options = {
            'analysis_type': 'advanced',
            'enable_refactoring': True,
            'enable_formatting': True,
            'enable_validation': True
        }
        optimizer = TreeSitterHLSLOptimizer(options)
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    def __init__(self, options=None):
        self.options = options or {}
        self.analysis_type = self.options.get('analysis_type', 'basic')
        self.enable_refactoring = self.options.get('enable_refactoring', False)
        self.enable_formatting = self.options.get('enable_formatting', False)
        self.enable_validation = self.options.get('enable_validation', False)

        so_path = os.path.join(os.path.dirname(__file__), 'tree-sitter-hlsl.so')
        self.language = Language(so_path, 'hlsl')
        self.parser = Parser()
        self.parser.set_language(self.language)

    def optimize_code(self, code: str) -> str:
        tree = self.parser.parse(bytes(code, 'utf8'))
        root = tree.root_node
        # 1. 收集所有变量声明和使用
        declared_vars = set()
        used_vars = set()
        def walk(node):
            if node.type == 'variable_declaration':
                name_node = node.child_by_field_name('declarator')
                if name_node:
                    declared_vars.add(code[name_node.start_byte:name_node.end_byte])
            if node.type == 'identifier':
                used_vars.add(code[node.start_byte:node.end_byte])
            for c in node.children:
                walk(c)
        walk(root)
        # 2. 删除未使用变量声明
        edits = []
        for node in root.children:
            if node.type == 'variable_declaration':
                name_node = node.child_by_field_name('declarator')
                if name_node:
                    var_name = code[name_node.start_byte:name_node.end_byte]
                    if var_name not in used_vars:
                        edits.append((node.start_byte, node.end_byte, ''))
        # 3. 死代码删除（if(false)、return后）
        def remove_dead_code(node):
            if node.type == 'if_statement':
                cond_node = node.child_by_field_name('condition')
                if cond_node and code[cond_node.start_byte:cond_node.end_byte].strip() == 'false':
                    edits.append((node.start_byte, node.end_byte, ''))
            if node.type == 'compound_statement':
                found_return = False
                for c in node.children:
                    if found_return:
                        edits.append((c.start_byte, c.end_byte, ''))
                    if c.type == 'return_statement':
                        found_return = True
            for c in node.children:
                remove_dead_code(c)
        remove_dead_code(root)
        # 逆序应用替换
        for start, end, rep in sorted(edits, reverse=True):
            code = code[:start] + rep + code[end:]
        return code 