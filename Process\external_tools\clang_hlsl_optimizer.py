"""
基于clang/libclang的HLSL语法树自动优化器。
依赖：pip install clang（或libclang），适合C风格HLSL。
"""
import clang.cindex
import tempfile, os

class ClangHLSLOptimizer:
    """
    用法：
        optimizer = ClangHLSLOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)

    或者带选项：
        options = {
            'optimization_level': 'O2',
            'enable_warnings': True,
            'enable_analysis': True
        }
        optimizer = ClangHLSLOptimizer(options)
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    def __init__(self, options=None):
        # clang.cindex.Config.set_library_file('/path/to/libclang.so')
        self.options = options or {}
        self.optimization_level = self.options.get('optimization_level', 'O1')
        self.enable_warnings = self.options.get('enable_warnings', False)
        self.enable_analysis = self.options.get('enable_analysis', False)

    def optimize_code(self, code: str) -> str:
        with tempfile.NamedTemporaryFile('w+', suffix='.hlsl', delete=False) as f:
            f.write(code)
            f.flush()
            fname = f.name
        index = clang.cindex.Index.create()
        tu = index.parse(fname, args=['-x', 'c++'])
        # 1. 收集所有变量声明和使用
        declared_vars = set()
        used_vars = set()
        for node in tu.cursor.walk_preorder():
            if node.kind == clang.cindex.CursorKind.VAR_DECL:
                declared_vars.add(node.spelling)
            if node.kind == clang.cindex.CursorKind.DECL_REF_EXPR:
                used_vars.add(node.spelling)
        # 2. 删除未使用变量声明
        edits = []
        for node in tu.cursor.walk_preorder():
            if node.kind == clang.cindex.CursorKind.VAR_DECL:
                if node.spelling not in used_vars:
                    edits.append((node.extent.start.offset, node.extent.end.offset, ''))
        # 3. 死代码删除（if(false)、return后）
        for node in tu.cursor.walk_preorder():
            if node.kind == clang.cindex.CursorKind.IF_stmt:
                cond = list(node.get_children())[0]
                if cond.kind == clang.cindex.CursorKind.INTEGER_LITERAL:
                    # 只处理if(false)的情况
                    token = list(cond.get_tokens())[0]
                    if token.spelling == '0':
                        edits.append((node.extent.start.offset, node.extent.end.offset, ''))
            if node.kind == clang.cindex.CursorKind.COMPOUND_STMT:
                found_return = False
                for c in node.get_children():
                    if found_return:
                        edits.append((c.extent.start.offset, c.extent.end.offset, ''))
                    if c.kind == clang.cindex.CursorKind.RETURN_STMT:
                        found_return = True
        # 逆序应用替换
        for start, end, rep in sorted(edits, reverse=True):
            code = code[:start] + rep + code[end:]
        os.unlink(fname)
        return code 