#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML生成中的运算过程显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer

def test_html_generation():
    """测试HTML生成中的运算过程显示"""
    analyzer = SyntaxTreeAnalyzer()
    
    # 测试代码
    test_code = """
half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));
float result = fast::clamp(1.0, 0.0, 1.0);
"""
    
    print("测试HTML生成中的运算过程显示")
    print("=" * 50)
    
    # 分析代码
    result = analyzer.analyze_shader_with_syntax_trees(test_code)
    
    print(f"分析结果键: {list(result.keys())}")
    print(f"行分析数量: {len(result.get('line_analyses', []))}")
    
    # 检查每行的运算过程
    for i, line_analysis in enumerate(result.get('line_analyses', [])):
        print(f"\n第 {i+1} 行分析:")
        print(f"  行号: {getattr(line_analysis, 'line_number', 'N/A')}")
        print(f"  行内容: {getattr(line_analysis, 'line_content', 'N/A')}")
        print(f"  有运算过程: {hasattr(line_analysis, 'operation_process') and bool(line_analysis.operation_process)}")
        
        if hasattr(line_analysis, 'operation_process') and line_analysis.operation_process:
            print(f"  运算过程数量: {len(line_analysis.operation_process)}")
            for j, op in enumerate(line_analysis.operation_process, 1):
                print(f"    {j}. {op.string}")
        else:
            print("  无运算过程")
    
    # 生成HTML报告并检查
    print("\n" + "=" * 50)
    print("生成HTML报告...")
    
    try:
        # 调用生成HTML报告的方法
        from Process.Analysis.analysis_report_generator import AnalysisReportGenerator
        
        generator = AnalysisReportGenerator()
        
        # 准备数据
        code_lines_data = []
        for i, line in enumerate(test_code.strip().split('\n'), 1):
            if line.strip():
                # 查找对应的分析结果
                has_analysis = False
                operation_count = 0
                conversion_count = 0
                
                for analysis in result.get('line_analyses', []):
                    if hasattr(analysis, 'line_number') and analysis.line_number == i:
                        has_analysis = True
                        if hasattr(analysis, 'operation_process'):
                            operation_count = len(analysis.operation_process)
                        if hasattr(analysis, 'type_conversions'):
                            conversion_count = len(analysis.type_conversions)
                        break
                
                from Process.Analysis.analysis_report_generator import CodeLineData
                line_data = CodeLineData(
                    line_number=i,
                    content=line,
                    has_analysis=has_analysis,
                    operation_count=operation_count,
                    conversion_count=conversion_count,
                    performance_level='medium' if operation_count > 0 else 'low',
                    css_classes=[]
                )
                code_lines_data.append(line_data)
        
        # 生成HTML
        html_content = generator.generate_precise_html_report(result, code_lines_data)
        
        # 检查HTML中是否包含运算过程
        operation_process_count = html_content.count('<div class="operation-process">')
        print(f"HTML中包含的运算过程块数量: {operation_process_count}")
        
        # 检查具体的运算过程内容
        if 'tmp_0 = dot(' in html_content:
            print("✅ 找到了 dot 函数调用的运算过程")
        else:
            print("❌ 没有找到 dot 函数调用的运算过程")
            
        if 'tmp_1 = fast::clamp(' in html_content:
            print("✅ 找到了 fast::clamp 函数调用的运算过程")
        else:
            print("❌ 没有找到 fast::clamp 函数调用的运算过程")
        
        # 保存HTML文件用于检查
        with open('test_html_output.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("HTML文件已保存为 test_html_output.html")
        
    except Exception as e:
        print(f"生成HTML时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_html_generation()
