"""
HLSL分支优化器 - 优化HLSL中的条件分支，减少分支发散
"""
import re
from typing import Dict, List, Tuple, Set
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel

class HLSLBranchOptimizer(HLSLOptimizerBase):
    """
    分支优化器，专门优化HLSL中的条件分支结构。
    
    支持的优化：
    - 分支消除（使用条件表达式）
    - 分支合并
    - 分支预测优化
    - 条件简化
    - 短路求值优化
    - 分支扁平化
    
    用法：
        optimizer = BranchOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 分支模式
        self.branch_patterns = {
            'simple_if': re.compile(r'if\s*\(\s*([^)]+)\s*\)\s*{([^}]*)}', re.DOTALL),
            'if_else': re.compile(r'if\s*\(\s*([^)]+)\s*\)\s*{([^}]*)}else\s*{([^}]*)}', re.DOTALL),
            'ternary': re.compile(r'([^=]+)=\s*([^?]+)\?\s*([^:]+):\s*([^;]+);'),
            'switch': re.compile(r'switch\s*\(\s*([^)]+)\s*\)\s*{([^}]*)}', re.DOTALL)
        }
    
    def optimize_code(self, code: str) -> str:
        """优化分支代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._eliminate_simple_branches(optimized_code)
            optimized_code = self._optimize_boolean_expressions(optimized_code)
        
        # 暂时禁用所有AGGRESSIVE级别的分支优化，因为它们可能导致大括号匹配问题
        # if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
        #     optimized_code = self._merge_similar_branches(optimized_code)
        #     optimized_code = self._flatten_nested_branches(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._convert_to_branchless(optimized_code)
            optimized_code = self._optimize_switch_statements(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _eliminate_simple_branches(self, code: str) -> str:
        """消除简单的分支结构"""
        optimizations = [
            # 优化：将简单的if-else转换为三元运算符
            (re.compile(r'if\s*\(\s*([^)]+)\s*\)\s*(\w+)\s*=\s*([^;]+);\s*else\s*\2\s*=\s*([^;]+);'),
             r'\2 = (\1) ? (\3) : (\4);'),

            # 优化：将return语句的if-else转换为三元运算符
            (re.compile(r'if\s*\(\s*([^)]+)\s*\)\s*return\s+([^;]+);\s*else\s*return\s+([^;]+);'),
             r'return (\1) ? (\2) : (\3);'),

            # 优化：将单语句if转换为短路求值
            (re.compile(r'if\s*\(\s*([^)]+)\s*\)\s*{\s*([^;]+);\s*}'),
             lambda m: f'({m.group(1)}) && ({m.group(2)});' if self._is_safe_for_short_circuit(m.group(2)) else m.group(0)),

            # 优化：消除常量条件分支
            (re.compile(r'if\s*\(\s*true\s*\)\s*{([^}]*)}else\s*{[^}]*}', re.DOTALL),
             r'\1'),
            (re.compile(r'if\s*\(\s*false\s*\)\s*{[^}]*}else\s*{([^}]*)}', re.DOTALL),
             r'\1'),
            (re.compile(r'if\s*\(\s*false\s*\)\s*{[^}]*}', re.DOTALL),
             r''),

            # 新增：优化简单的if-else分支（多行格式）
            (re.compile(r'if\s*\(\s*([^)]+)\s*\)\s*\{\s*([^}]+)\s*\}\s*else\s*\{\s*([^}]+)\s*\}', re.DOTALL),
             lambda m: self._convert_multiline_if_else(m)),

            # 新增：优化三元运算符中的常量
            (re.compile(r'(\w+)\s*=\s*true\s*\?\s*([^:]+)\s*:\s*([^;]+);'),
             r'\1 = \2;'),
            (re.compile(r'(\w+)\s*=\s*false\s*\?\s*([^:]+)\s*:\s*([^;]+);'),
             r'\1 = \3;'),
        ]

        optimized_code = code
        for pattern, replacement in optimizations:
            if callable(replacement):
                matches = list(pattern.finditer(optimized_code))
                for match in reversed(matches):
                    new_text = replacement(match)
                    if new_text != match.group(0):
                        optimized_code = optimized_code[:match.start()] + new_text + optimized_code[match.end():]
                        self.statistics['optimizations_applied'] += 1
            else:
                matches = len(pattern.findall(optimized_code))
                optimized_code = pattern.sub(replacement, optimized_code)
                self.statistics['optimizations_applied'] += matches

        return optimized_code

    def _convert_multiline_if_else(self, match) -> str:
        """转换多行if-else为三元运算符"""
        condition = match.group(1).strip()
        true_branch = match.group(2).strip()
        false_branch = match.group(3).strip()

        # 检查是否是简单的赋值语句
        true_assign = re.match(r'(\w+)\s*=\s*([^;]+);?', true_branch)
        false_assign = re.match(r'(\w+)\s*=\s*([^;]+);?', false_branch)

        if true_assign and false_assign and true_assign.group(1) == false_assign.group(1):
            var_name = true_assign.group(1)
            true_value = true_assign.group(2)
            false_value = false_assign.group(2)
            self.statistics['optimizations_applied'] += 1
            return f'{var_name} = ({condition}) ? ({true_value}) : ({false_value});'

        return match.group(0)  # 不优化，返回原文
    
    def _optimize_boolean_expressions(self, code: str) -> str:
        """优化布尔表达式"""
        optimizations = [
            # 双重否定消除
            (re.compile(r'!\s*!\s*\(([^)]+)\)'), r'(\1)'),
            (re.compile(r'!\s*!\s*(\w+)'), r'\1'),
            
            # 德摩根定律
            (re.compile(r'!\s*\(\s*([^&|]+)\s*&&\s*([^)]+)\s*\)'), r'(!\1 || !\2)'),
            (re.compile(r'!\s*\(\s*([^&|]+)\s*\|\|\s*([^)]+)\s*\)'), r'(!\1 && !\2)'),
            
            # 布尔常量优化
            (re.compile(r'(\w+)\s*&&\s*true'), r'\1'),
            (re.compile(r'true\s*&&\s*(\w+)'), r'\1'),
            (re.compile(r'(\w+)\s*\|\|\s*false'), r'\1'),
            (re.compile(r'false\s*\|\|\s*(\w+)'), r'\1'),
            (re.compile(r'(\w+)\s*&&\s*false'), r'false'),
            (re.compile(r'false\s*&&\s*(\w+)'), r'false'),
            (re.compile(r'(\w+)\s*\|\|\s*true'), r'true'),
            (re.compile(r'true\s*\|\|\s*(\w+)'), r'true'),
            
            # 比较优化
            (re.compile(r'(\w+)\s*==\s*true'), r'\1'),
            (re.compile(r'(\w+)\s*==\s*false'), r'!\1'),
            (re.compile(r'(\w+)\s*!=\s*true'), r'!\1'),
            (re.compile(r'(\w+)\s*!=\s*false'), r'\1'),
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _merge_similar_branches(self, code: str) -> str:
        """合并相似的分支"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 查找连续的if语句
            if re.search(r'if\s*\(', line) and i + 2 < len(lines):
                current_if = self._extract_if_statement(lines, i)
                
                if current_if:
                    next_line_idx = current_if['end'] + 1
                    
                    # 跳过空行
                    while (next_line_idx < len(lines) and 
                           not lines[next_line_idx].strip()):
                        next_line_idx += 1
                    
                    if (next_line_idx < len(lines) and 
                        re.search(r'if\s*\(', lines[next_line_idx])):
                        
                        next_if = self._extract_if_statement(lines, next_line_idx)
                        
                        if (next_if and 
                            self._can_merge_if_statements(current_if, next_if)):
                            
                            # 合并if语句
                            merged = self._merge_if_statements(current_if, next_if)
                            optimized_lines.extend(merged)
                            
                            self.statistics['optimizations_applied'] += 1
                            i = next_if['end'] + 1
                            continue
                    
                    # 如果不能合并，添加原始if语句
                    optimized_lines.extend(lines[i:current_if['end']+1])
                    i = current_if['end'] + 1
                    continue
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _flatten_nested_branches(self, code: str) -> str:
        """扁平化嵌套分支"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 查找嵌套的if语句
            if re.search(r'if\s*\(', line):
                if_info = self._extract_if_statement(lines, i)
                
                if if_info and self._has_nested_if(if_info['body']):
                    # 尝试扁平化
                    flattened = self._flatten_if_statement(if_info)
                    
                    if flattened:
                        optimized_lines.append('    // Flattened nested conditions')
                        optimized_lines.extend(flattened)
                        self.statistics['optimizations_applied'] += 1
                        i = if_info['end'] + 1
                        continue
                
                # 如果不能扁平化，添加原始语句
                optimized_lines.extend(lines[i:if_info['end']+1])
                i = if_info['end'] + 1
                continue
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _convert_to_branchless(self, code: str) -> str:
        """转换为无分支代码"""
        optimizations = [
            # abs(x) 的分支版本转换为内置函数
            (re.compile(r'if\s*\(\s*(\w+)\s*<\s*0\s*\)\s*\1\s*=\s*-\1;'),
             r'\1 = abs(\1);'),
            
            # max/min 的分支版本
            (re.compile(r'if\s*\(\s*(\w+)\s*>\s*(\w+)\s*\)\s*(\w+)\s*=\s*\1;\s*else\s*\3\s*=\s*\2;'),
             r'\3 = max(\1, \2);'),
            (re.compile(r'if\s*\(\s*(\w+)\s*<\s*(\w+)\s*\)\s*(\w+)\s*=\s*\1;\s*else\s*\3\s*=\s*\2;'),
             r'\3 = min(\1, \2);'),
            
            # clamp 的分支版本
            (re.compile(r'if\s*\(\s*(\w+)\s*<\s*(\w+)\s*\)\s*\1\s*=\s*\2;\s*if\s*\(\s*\1\s*>\s*(\w+)\s*\)\s*\1\s*=\s*\3;'),
             r'\1 = clamp(\1, \2, \3);'),
            
            # step 函数
            (re.compile(r'if\s*\(\s*(\w+)\s*<\s*(\w+)\s*\)\s*(\w+)\s*=\s*0;\s*else\s*\3\s*=\s*1;'),
             r'\3 = step(\2, \1);'),
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _optimize_switch_statements(self, code: str) -> str:
        """优化switch语句"""
        def optimize_switch(match):
            switch_var = match.group(1)
            switch_body = match.group(2)
            
            # 分析case数量
            cases = re.findall(r'case\s+(\d+):', switch_body)
            
            if len(cases) <= 3:
                # 小的switch转换为if-else链
                if_chain = self._convert_switch_to_if_chain(switch_var, switch_body)
                if if_chain:
                    self.statistics['optimizations_applied'] += 1
                    return if_chain
            
            return match.group(0)
        
        switch_pattern = re.compile(r'switch\s*\(\s*([^)]+)\s*\)\s*{([^}]*)}', re.DOTALL)
        return switch_pattern.sub(optimize_switch, code)
    
    def _is_safe_for_short_circuit(self, statement: str) -> bool:
        """检查语句是否可以安全地用于短路求值"""
        # 避免有副作用的操作
        unsafe_patterns = ['++', '--', '=', 'Sample', 'Load']
        return not any(pattern in statement for pattern in unsafe_patterns)
    
    def _extract_if_statement(self, lines: List[str], start_idx: int) -> Dict:
        """提取if语句信息"""
        if start_idx >= len(lines):
            return None
        
        brace_count = 0
        end_idx = start_idx
        
        for i in range(start_idx, len(lines)):
            line = lines[i]
            if '{' in line:
                brace_count += line.count('{')
            if '}' in line:
                brace_count -= line.count('}')
            
            if brace_count == 0 and i > start_idx:
                end_idx = i
                break
        
        # 提取条件
        condition_match = re.search(r'if\s*\(\s*([^)]+)\s*\)', lines[start_idx])
        condition = condition_match.group(1) if condition_match else ""
        
        return {
            'start': start_idx,
            'end': end_idx,
            'condition': condition,
            'body': lines[start_idx+1:end_idx]
        }
    
    def _can_merge_if_statements(self, if1: Dict, if2: Dict) -> bool:
        """检查是否可以合并两个if语句"""
        # 简化的实现：检查是否有相同的操作
        body1_str = ' '.join(if1['body'])
        body2_str = ' '.join(if2['body'])
        
        # 如果操作相似，可以考虑合并条件
        return len(body1_str) < 50 and len(body2_str) < 50
    
    def _merge_if_statements(self, if1: Dict, if2: Dict) -> List[str]:
        """合并两个if语句"""
        merged_condition = f"({if1['condition']}) || ({if2['condition']})"
        merged_lines = [f"if ({merged_condition}) {{"]
        merged_lines.extend(if1['body'])
        merged_lines.extend(if2['body'])
        merged_lines.append('}')
        return merged_lines
    
    def _has_nested_if(self, body_lines: List[str]) -> bool:
        """检查是否有嵌套的if语句"""
        return any('if' in line for line in body_lines)
    
    def _flatten_if_statement(self, if_info: Dict) -> List[str]:
        """扁平化if语句"""
        # 简化的实现
        flattened = []
        
        for line in if_info['body']:
            if 'if' in line:
                # 提取嵌套条件
                nested_match = re.search(r'if\s*\(\s*([^)]+)\s*\)', line)
                if nested_match:
                    nested_condition = nested_match.group(1)
                    combined_condition = f"({if_info['condition']}) && ({nested_condition})"
                    flattened.append(f"if ({combined_condition}) {{")
                    continue
            
            flattened.append(line)
        
        return flattened if len(flattened) < len(if_info['body']) + 2 else None
    
    def _convert_switch_to_if_chain(self, switch_var: str, switch_body: str) -> str:
        """将switch转换为if-else链"""
        cases = re.findall(r'case\s+(\d+):\s*([^b]*?)(?=case|\}|default)', switch_body, re.DOTALL)
        
        if not cases:
            return None
        
        if_chain = []
        for i, (case_val, case_body) in enumerate(cases):
            condition = f"{switch_var} == {case_val}"
            
            if i == 0:
                if_chain.append(f"if ({condition}) {{")
            else:
                if_chain.append(f"}} else if ({condition}) {{")
            
            if_chain.append(case_body.strip())
        
        if_chain.append('}')
        return '\n'.join(if_chain)
