"""
基于外部工具的HLSL优化器模块

包含依赖外部工具的优化器：
- ClangHLSLOptimizer: 基于Clang的优化器
- TreeSitterHLSLOptimizer: 基于Tree-sitter的优化器
- SPIRVToolsHLSLOptimizer: 基于SPIR-V工具链的优化器

注意：这些优化器需要安装相应的外部工具才能正常工作
"""

try:
    from .clang_hlsl_optimizer import ClangHLSLOptimizer
    _CLANG_AVAILABLE = True
except ImportError:
    _CLANG_AVAILABLE = False

try:
    from .tree_sitter_hlsl_optimizer import TreeSitterHLSLOptimizer
    _TREE_SITTER_AVAILABLE = True
except ImportError:
    _TREE_SITTER_AVAILABLE = False

try:
    from .spirvtools_hlsl_optimizer import SPIRVToolsHLSLOptimizer
    _SPIRV_TOOLS_AVAILABLE = True
except ImportError:
    _SPIRV_TOOLS_AVAILABLE = False

# 只导出可用的优化器
__all__ = []

if _CLANG_AVAILABLE:
    __all__.append('ClangHLSLOptimizer')

if _TREE_SITTER_AVAILABLE:
    __all__.append('TreeSitterHLSLOptimizer')

if _SPIRV_TOOLS_AVAILABLE:
    __all__.append('SPIRVToolsHLSLOptimizer')

# 提供可用性检查函数
def get_available_optimizers():
    """获取可用的外部工具优化器列表"""
    available = []
    if _CLANG_AVAILABLE:
        available.append('ClangHLSLOptimizer')
    if _TREE_SITTER_AVAILABLE:
        available.append('TreeSitterHLSLOptimizer')
    if _SPIRV_TOOLS_AVAILABLE:
        available.append('SPIRVToolsHLSLOptimizer')
    return available

def check_dependencies():
    """检查外部工具依赖"""
    status = {
        'clang': _CLANG_AVAILABLE,
        'tree_sitter': _TREE_SITTER_AVAILABLE,
        'spirv_tools': _SPIRV_TOOLS_AVAILABLE
    }
    return status
