"""
GLSL向量化优化器 - 将标量操作转换为向量操作，提高并行度
"""
import re
from typing import Dict, List, Tuple, Set
from .glsl_optimizer_base import GLSLOptimizerBase, OptimizationLevel

class GLSLVectorizationOptimizer(GLSLOptimizerBase):
    """
    GLSL向量化优化器，将标量操作转换为向量操作以提高并行度。
    
    支持的优化：
    - 标量运算向量化
    - 数组访问向量化
    - 函数调用向量化
    - 条件操作向量化
    - 循环向量化
    - swizzle操作优化
    
    用法：
        optimizer = GLSLVectorizationOptimizer()
        optimized_code = optimizer.optimize_code(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # GLSL向量化模式
        self.vectorization_patterns = {
            'scalar_ops': re.compile(r'(\w+)\s*([+\-*/])\s*(\w+)'),
            'array_access': re.compile(r'(\w+)\[(\d+)\]'),
            'function_calls': re.compile(r'(\w+)\s*\(\s*([^)]+)\s*\)'),
            'assignments': re.compile(r'(\w+)\s*=\s*([^;]+);')
        }
        
        # GLSL向量类型映射
        self.vector_types = {
            'float': ['vec2', 'vec3', 'vec4'],
            'int': ['ivec2', 'ivec3', 'ivec4'],
            'uint': ['uvec2', 'uvec3', 'uvec4'],
            'bool': ['bvec2', 'bvec3', 'bvec4']
        }
        
        # swizzle组合
        self.swizzle_patterns = {
            2: ['xy', 'xz', 'yz', 'rg', 'rb', 'gb'],
            3: ['xyz', 'xzy', 'yxz', 'yzx', 'zxy', 'zyx', 'rgb', 'rbg', 'grb', 'gbr', 'brg', 'bgr'],
            4: ['xyzw', 'rgba']
        }
    
    def optimize_code(self, code: str) -> str:
        """优化GLSL向量化代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._optimize_scalar_operations(optimized_code)
            optimized_code = self._optimize_swizzle_operations(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._vectorize_array_operations(optimized_code)
            optimized_code = self._optimize_vector_constructors(optimized_code)
            optimized_code = self._merge_vector_operations(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._vectorize_loops(optimized_code)
            optimized_code = self._optimize_conditional_operations(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _optimize_scalar_operations(self, code: str) -> str:
        """优化标量运算为向量运算"""
        # 优化：检测并向量化连续的标量运算
        lines = code.splitlines()
        optimized_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            if self._is_vectorizable_scalar_op(line):
                # 优化：收集相似的标量运算进行向量化
                similar_ops = [line]
                j = i + 1

                while j < len(lines) and self._is_similar_operation(line, lines[j].strip()):
                    similar_ops.append(lines[j].strip())
                    j += 1

                # 优化：当找到足够的相似运算时进行向量化
                if len(similar_ops) >= 2:
                    vectorized = self._vectorize_operations(similar_ops)
                    if vectorized:
                        optimized_lines.extend(vectorized)
                        i = j
                        self.statistics['optimizations_applied'] += 1
                        continue

            optimized_lines.append(lines[i])
            i += 1

        return '\n'.join(optimized_lines)
    
    def _optimize_swizzle_operations(self, code: str) -> str:
        """优化swizzle操作"""
        optimizations = [
            # 优化：简化冗余的swizzle链
            (re.compile(r'(\w+)\.xyz\.xy'), r'\1.xy'),
            (re.compile(r'(\w+)\.xyzw\.xyz'), r'\1.xyz'),

            # 优化：处理复杂的swizzle组合
            (re.compile(r'(\w+)\.([xyzw]+)\.([xyzw]+)'), self._simplify_swizzle)
        ]

        for pattern, replacement in optimizations:
            old_code = code
            if callable(replacement):
                code = pattern.sub(replacement, code)
            else:
                code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _vectorize_array_operations(self, code: str) -> str:
        """向量化数组操作"""
        # 优化：分析数组访问模式并实现向量化加载
        return code
    
    def _optimize_vector_constructors(self, code: str) -> str:
        """优化向量构造函数"""
        optimizations = [
            # 优化：简化向量构造后的swizzle操作
            (re.compile(r'vec3\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*[^)]+\s*\)\.xy'), r'vec2(\1, \2)'),
            (re.compile(r'vec4\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^,]+)\s*,\s*[^)]+\s*\)\.xyz'), r'vec3(\1, \2, \3)'),

            # 优化：消除嵌套的向量构造
            (re.compile(r'vec2\s*\(\s*vec2\s*\(\s*([^)]+)\s*\)\s*\)'), r'vec2(\1)'),
            (re.compile(r'vec3\s*\(\s*vec3\s*\(\s*([^)]+)\s*\)\s*\)'), r'vec3(\1)'),
            (re.compile(r'vec4\s*\(\s*vec4\s*\(\s*([^)]+)\s*\)\s*\)'), r'vec4(\1)')
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _merge_vector_operations(self, code: str) -> str:
        """合并向量操作"""
        # 优化：将分散的向量分量赋值合并为向量赋值
        lines = code.splitlines()
        optimized_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 优化：检测向量分量赋值模式
            match = re.match(r'(\w+)\.([xyzw])\s*=\s*([^;]+);', line)
            if match:
                var_name = match.group(1)
                component = match.group(2)
                value = match.group(3)

                # 优化：收集连续的分量赋值
                assignments = [(component, value)]
                j = i + 1

                while j < len(lines):
                    next_line = lines[j].strip()
                    next_match = re.match(rf'{re.escape(var_name)}\.([xyzw])\s*=\s*([^;]+);', next_line)
                    if next_match:
                        assignments.append((next_match.group(1), next_match.group(2)))
                        j += 1
                    else:
                        break

                # 优化：合并多个分量赋值为向量赋值
                if len(assignments) >= 2:
                    merged = self._merge_component_assignments(var_name, assignments)
                    if merged:
                        optimized_lines.append(merged)
                        i = j
                        self.statistics['optimizations_applied'] += 1
                        continue

            optimized_lines.append(lines[i])
            i += 1

        return '\n'.join(optimized_lines)
    
    def _vectorize_loops(self, code: str) -> str:
        """向量化循环"""
        # 优化：分析循环结构并实现向量化转换
        return code
    
    def _optimize_conditional_operations(self, code: str) -> str:
        """优化条件操作为向量操作"""
        # 优化：将条件表达式转换为向量化的mix函数调用
        return code
    
    def _is_vectorizable_scalar_op(self, line: str) -> bool:
        """检查是否为可向量化的标量运算"""
        # 优化：改进标量运算检测逻辑
        return bool(re.match(r'\s*\w+\s*=\s*\w+\s*[+\-*/]\s*\w+\s*;', line))
    
    def _is_similar_operation(self, line1: str, line2: str) -> bool:
        """检查两个操作是否相似（可以向量化）"""
        # 优化：改进相似操作检测算法
        pattern1 = re.sub(r'\w+', 'VAR', line1)
        pattern2 = re.sub(r'\w+', 'VAR', line2)
        return pattern1 == pattern2
    
    def _vectorize_operations(self, operations: List[str]) -> List[str]:
        """将多个标量操作向量化"""
        # 优化：实现标量操作到向量操作的转换逻辑
        return None
    
    def _simplify_swizzle(self, match):
        """简化swizzle操作"""
        var = match.group(1)
        swizzle1 = match.group(2)
        swizzle2 = match.group(3)

        # 优化：实现swizzle链的简化逻辑
        return match.group(0)
    
    def _merge_component_assignments(self, var_name: str, assignments: List[Tuple[str, str]]) -> str:
        """合并分量赋值"""
        # 检查是否可以合并为向量赋值
        components = [comp for comp, _ in assignments]
        values = [val for _, val in assignments]
        
        # 检查分量是否连续
        if set(components) == {'x', 'y'} and len(assignments) == 2:
            # 重新排序以确保正确的顺序
            sorted_assignments = sorted(assignments, key=lambda x: 'xy'.index(x[0]))
            values = [val for _, val in sorted_assignments]
            return f"{var_name}.xy = vec2({', '.join(values)});"
        elif set(components) == {'x', 'y', 'z'} and len(assignments) == 3:
            sorted_assignments = sorted(assignments, key=lambda x: 'xyz'.index(x[0]))
            values = [val for _, val in sorted_assignments]
            return f"{var_name}.xyz = vec3({', '.join(values)});"
        elif set(components) == {'x', 'y', 'z', 'w'} and len(assignments) == 4:
            sorted_assignments = sorted(assignments, key=lambda x: 'xyzw'.index(x[0]))
            values = [val for _, val in sorted_assignments]
            return f"{var_name}.xyzw = vec4({', '.join(values)});"
        
        return None
