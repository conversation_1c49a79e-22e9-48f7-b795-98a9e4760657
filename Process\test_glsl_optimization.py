"""
GLSL优化测试脚本

流程：
1. 读取输入GLSL文件
2. 使用GLSLOptimizerManager进行优化
3. 输出优化后的代码到input.glsl
4. 调用malioc -c Mali-G76 input.glsl进行分析
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加路径以便导入模块
current_dir = Path(__file__).parent
sys.path.append(str(current_dir.parent))

from basic_optimizers import GLSLOptimizerManager, OptimizationLevel

class GLSLOptimizationTester:
    def __init__(self, gpu_target="Mali-G76"):
        self.manager = GLSLOptimizerManager(OptimizationLevel.AGGRESSIVE)
        self.original_file = "original.frag"
        self.optimized_file = "optimized.frag"
        self.comparison_file = "comparison_analysis.txt"
        self.gpu_target = gpu_target

        # 支持的GPU目标
        self.supported_gpus = [
            "Mali-G76", "Mali-G77", "Mali-G78", "Mali-G710", "Mali-G715",
            "Mali-G57", "Mali-G68", "Mali-G52", "Mali-G51", "Mali-G31"
        ]
        
        # 测试用的GLSL代码（你可以手动补全）
        self.test_glsl_code = """
// 测试GLSL着色器 - 请手动补全或替换为实际文件内容
#version 330 core

// 输入变量
in vec3 position;
in vec3 normal;
in vec2 texCoord;

// 输出变量
out vec4 fragColor;

// Uniform变量
uniform mat4 modelMatrix;
uniform mat4 viewMatrix;
uniform mat4 projMatrix;
uniform vec3 lightDirection;
uniform vec3 lightColor;
uniform sampler2D mainTexture;

void main()
{
    // 位置变换 - 可以优化的数学运算
    vec4 worldPos = modelMatrix * vec4(position, 1.0);
    vec4 viewPos = viewMatrix * worldPos;
    gl_Position = projMatrix * viewPos;
    
    // 法线变换
    vec3 worldNormal = normalize(mat3(modelMatrix) * normal);
    
    // 光照计算 - 包含可优化的数学函数
    vec3 lightDir = normalize(lightDirection);
    
    // 一些可以优化的数学运算
    float distance = sqrt(dot(worldPos.xyz, worldPos.xyz));
    float attenuation = 1.0 / (1.0 + distance * distance);
    
    // 分支优化测试
    vec3 finalColor;
    if (attenuation > 0.5)
    {
        finalColor = lightColor * 1.2;
    }
    else
    {
        finalColor = lightColor * 0.8;
    }
    
    // 循环优化测试
    for (int i = 0; i < 4; i++)
    {
        finalColor += vec3(0.1);
    }
    
    // 幂函数优化测试
    float brightness = pow(attenuation, 2.0) + pow(distance, 3.0);
    finalColor *= brightness;
    
    // 向量化优化测试
    float r = finalColor.r * 2.0;
    float g = finalColor.g * 2.0;
    float b = finalColor.b * 2.0;
    
    // 纹理采样
    vec4 texColor = texture(mainTexture, texCoord);
    
    // 最终颜色输出
    fragColor = vec4(r, g, b, 1.0) * texColor;
}
        """.strip()
    
    def read_glsl_file(self, file_path):
        """读取GLSL文件"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"✓ 成功读取文件: {file_path}")
                print(f"  文件大小: {len(content)} 字符")
                return content
            else:
                print(f"⚠ 文件不存在: {file_path}")
                print("  使用默认测试代码")
                return self.test_glsl_code
        except Exception as e:
            print(f"✗ 读取文件失败: {e}")
            print("  使用默认测试代码")
            return self.test_glsl_code
    
    def optimize_glsl(self, glsl_code):
        """使用GLSLOptimizerManager优化GLSL代码"""
        print("\n=== 开始GLSL优化 ===")
        
        try:
            # 获取所有可用的优化器
            available_optimizers = self.manager.get_available_optimizers()
            print(f"✓ 可用优化器: {len(available_optimizers)}个")
            print(f"  优化器列表: {available_optimizers}")
            
            # 分析代码特征
            characteristics = self.manager.analyze_code_characteristics(glsl_code)
            print(f"✓ 代码特征分析完成")
            print(f"  代码行数: {characteristics['line_count']}")
            print(f"  复杂度分数: {characteristics['complexity_score']}")
            print(f"  推荐优化器: {characteristics['recommended_optimizers']}")
            
            # 执行优化（只使用安全的优化器）
            print(f"\n开始执行优化...")
            safe_optimizers = ['precision_analyzer']  # 只使用精度分析器
            optimized_code = self.manager.optimize_all(glsl_code, safe_optimizers)
            
            # 获取统计信息
            stats = self.manager.get_total_statistics()
            print(f"\n✓ 优化完成")
            print(f"  应用的优化次数: {stats['total_optimizations_applied']}")
            print(f"  处理的代码行数: {stats['total_lines_processed']}")
            print(f"  使用的优化器数量: {stats['optimizers_used']}")
            
            # 显示详细统计
            if stats['individual_stats']:
                print(f"\n详细统计信息:")
                for optimizer_name, optimizer_stats in stats['individual_stats'].items():
                    if 'optimizations_applied' in optimizer_stats:
                        print(f"  {optimizer_name}: {optimizer_stats['optimizations_applied']}次优化")
            
            return optimized_code
            
        except Exception as e:
            print(f"✗ 优化失败: {e}")
            import traceback
            traceback.print_exc()
            return glsl_code
    
    def write_shader_files(self, original_code, optimized_code):
        """将原始代码和优化后的代码分别写入文件"""
        try:
            # 写入原始文件
            with open(self.original_file, 'w', encoding='utf-8') as f:
                f.write(original_code)
            print(f"\n✓ 原始代码已写入: {self.original_file}")
            print(f"  文件大小: {len(original_code)} 字符")

            # 写入优化后的文件
            with open(self.optimized_file, 'w', encoding='utf-8') as f:
                f.write(optimized_code)
            print(f"✓ 优化后的代码已写入: {self.optimized_file}")
            print(f"  文件大小: {len(optimized_code)} 字符")

            return True
        except Exception as e:
            print(f"✗ 写入文件失败: {e}")
            return False

    def detect_shader_type(self, file_path):
        """检测着色器类型"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()

            # 检测着色器类型的关键字，返回malioc支持的格式
            if 'gl_position' in content or 'gl_vertex' in content:
                return 'vertex'  # 顶点着色器
            elif 'gl_fragcolor' in content or 'gl_frag' in content or 'fragcolor' in content:
                return 'fragment'  # 片段着色器
            elif 'gl_workgroupsize' in content or 'local_size' in content:
                return 'compute'  # 计算着色器
            elif 'gl_tesslevel' in content:
                return 'tessellation_control'  # 细分控制着色器
            elif 'gl_tesscoord' in content:
                return 'tessellation_evaluation'  # 细分评估着色器
            elif 'gl_in' in content and 'gl_out' in content:
                return 'geometry'  # 几何着色器
            else:
                # 默认假设为片段着色器
                return 'fragment'
        except:
            return 'fragment'

    def analyze_single_file(self, file_path, file_description):
        """使用malioc分析单个GLSL文件"""
        print(f"\n=== 分析{file_description} ===")

        # 检测着色器类型
        shader_type = self.detect_shader_type(file_path)
        print(f"检测到着色器类型: {shader_type}")

        # 构建malioc命令
        # 简化malioc命令，只使用基本参数
        malioc_command = [
            "malioc",
            "-c", self.gpu_target,  # 目标GPU
            file_path               # 输入文件
        ]
        
        try:
            print(f"执行命令: {' '.join(malioc_command)}")
            
            # 执行分析（使用字节模式避免编码问题）
            result = subprocess.run(
                malioc_command,
                capture_output=True,
                cwd=os.getcwd()
            )

            # 手动解码输出，忽略编码错误
            try:
                stdout_text = result.stdout.decode('utf-8', errors='ignore')
            except:
                stdout_text = result.stdout.decode('gbk', errors='ignore')

            try:
                stderr_text = result.stderr.decode('utf-8', errors='ignore')
            except:
                stderr_text = result.stderr.decode('gbk', errors='ignore')
            
            # 构建分析结果
            analysis_result = {
                'file_path': file_path,
                'description': file_description,
                'command': ' '.join(malioc_command),
                'return_code': result.returncode,
                'stdout': stdout_text,
                'stderr': stderr_text,
                'success': result.returncode == 0
            }
            
            if result.returncode == 0:
                print(f"✓ {file_description}分析成功!")

                if stdout_text:
                    print(f"=== {file_description}分析结果摘要 ===")
                    # 显示关键信息
                    lines = stdout_text.split('\n')
                    for line in lines:
                        if any(keyword in line.lower() for keyword in
                               ['cycles', 'instructions', 'registers', 'performance', 'warning', 'error']):
                            print(f"  {line}")

                return analysis_result
            else:
                print(f"✗ {file_description}分析失败 (返回码: {result.returncode})")
                if stderr_text:
                    print(f"  错误信息: {stderr_text}")
                if stdout_text:
                    print(f"  输出信息: {stdout_text}")
                return analysis_result
                
        except FileNotFoundError:
            print("✗ 找不到malioc工具")
            print("  请确保Mali Offline Compiler已安装并在PATH环境变量中")
            print("  下载地址: https://developer.arm.com/tools-and-software/graphics-and-gaming/mali-offline-compiler")
            return False
        except Exception as e:
            print(f"✗ 分析过程中发生错误: {e}")
            return analysis_result

    def compare_analysis_results(self, original_result, optimized_result):
        """对比分析结果并生成报告"""
        print(f"\n=== 性能对比分析 ===")

        # 构建对比报告
        comparison_report = "=== GLSL优化前后性能对比报告 ===\n\n"
        comparison_report += f"目标GPU: {self.gpu_target}\n"
        comparison_report += f"分析时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # 原始文件分析结果
        comparison_report += f"=== 优化前文件分析 ===\n"
        comparison_report += f"文件: {original_result['file_path']}\n"
        comparison_report += f"命令: {original_result['command']}\n"
        comparison_report += f"状态: {'成功' if original_result['success'] else '失败'}\n\n"
        if original_result['stdout']:
            comparison_report += f"分析结果:\n{original_result['stdout']}\n\n"
        if original_result['stderr']:
            comparison_report += f"错误信息:\n{original_result['stderr']}\n\n"

        # 优化后文件分析结果
        comparison_report += f"=== 优化后文件分析 ===\n"
        comparison_report += f"文件: {optimized_result['file_path']}\n"
        comparison_report += f"命令: {optimized_result['command']}\n"
        comparison_report += f"状态: {'成功' if optimized_result['success'] else '失败'}\n\n"
        if optimized_result['stdout']:
            comparison_report += f"分析结果:\n{optimized_result['stdout']}\n\n"
        if optimized_result['stderr']:
            comparison_report += f"错误信息:\n{optimized_result['stderr']}\n\n"

        # 性能对比
        if original_result['success'] and optimized_result['success']:
            comparison_report += f"=== 性能对比总结 ===\n"
            comparison_report += self._extract_performance_metrics(original_result['stdout'], optimized_result['stdout'])

        # 保存对比报告
        try:
            with open(self.comparison_file, 'w', encoding='utf-8') as f:
                f.write(comparison_report)
            print(f"✓ 对比分析报告已保存到: {self.comparison_file}")
            return True
        except Exception as e:
            print(f"✗ 保存对比报告失败: {e}")
            return False

    def _extract_performance_metrics(self, original_output, optimized_output):
        """提取并对比性能指标"""
        def extract_metrics(output):
            metrics = {}
            lines = output.split('\n')
            for line in lines:
                if 'Work registers:' in line:
                    metrics['work_registers'] = line.strip()
                elif 'Uniform registers:' in line:
                    metrics['uniform_registers'] = line.strip()
                elif 'Total instruction cycles:' in line:
                    metrics['total_cycles'] = line.strip()
                elif 'Shortest path cycles:' in line:
                    metrics['shortest_cycles'] = line.strip()
                elif 'Longest path cycles:' in line:
                    metrics['longest_cycles'] = line.strip()
            return metrics

        original_metrics = extract_metrics(original_output)
        optimized_metrics = extract_metrics(optimized_output)

        comparison = "性能指标对比:\n"
        comparison += f"{'指标':<20} {'优化前':<30} {'优化后':<30} {'变化':<10}\n"
        comparison += "-" * 90 + "\n"

        for key in ['work_registers', 'uniform_registers', 'total_cycles', 'shortest_cycles', 'longest_cycles']:
            original_val = original_metrics.get(key, 'N/A')
            optimized_val = optimized_metrics.get(key, 'N/A')

            # 简单的变化分析
            change = "N/A"
            if original_val != 'N/A' and optimized_val != 'N/A':
                if original_val == optimized_val:
                    change = "无变化"
                else:
                    change = "有变化"

            comparison += f"{key:<20} {original_val:<30} {optimized_val:<30} {change:<10}\n"

        return comparison + "\n"

    def run_test(self, input_glsl_file=None):
        """运行完整的测试流程"""
        print("=== GLSL优化和Mali GPU分析测试开始 ===")
        
        # 步骤1: 读取输入GLSL文件
        if input_glsl_file:
            glsl_code = self.read_glsl_file(input_glsl_file)
        else:
            print("使用默认测试GLSL代码")
            glsl_code = self.test_glsl_code
        
        # 步骤2: 优化GLSL代码
        optimized_code = self.optimize_glsl(glsl_code)

        # 步骤3: 输出原始代码和优化后的代码
        if not self.write_shader_files(glsl_code, optimized_code):
            return False

        # 步骤4: 分别分析原始文件和优化后的文件
        print(f"\n=== 开始Mali GPU对比分析 ===")
        original_result = self.analyze_single_file(self.original_file, "原始文件")
        optimized_result = self.analyze_single_file(self.optimized_file, "优化后文件")

        # 步骤5: 生成对比报告
        comparison_success = self.compare_analysis_results(original_result, optimized_result)
        
        print(f"\n=== 测试完成 ===")
        both_success = original_result['success'] and optimized_result['success']
        if both_success and comparison_success:
            print("🎉 所有步骤都成功完成!")
            print(f"📊 查看详细对比分析结果: {self.comparison_file}")
        elif both_success:
            print("⚠ Mali GPU分析成功，但对比报告生成失败")
        else:
            print("⚠ 部分Mali GPU分析步骤失败")
            if not original_result['success']:
                print("  - 原始文件分析失败")
            if not optimized_result['success']:
                print("  - 优化后文件分析失败")

        return both_success and comparison_success

def main():
    """主函数"""
    # 可以指定不同的GPU目标
    gpu_target = "Mali-G76"  # 可以修改为其他支持的GPU
    tester = GLSLOptimizationTester(gpu_target)

    print(f"目标GPU: {gpu_target}")
    print(f"支持的GPU: {', '.join(tester.supported_gpus)}")
    print()

    # 你可以在这里指定输入文件路径
    # input_file = "path/to/your/shader.glsl"  # 手动补全文件路径
    input_file = "D:/XuSong/Project/shaderProfile/shader/TestShader/PerformanceTestGL.frag"  # 等待手动输入文件路径
    
    print("=== GLSL优化和Mali GPU分析工具 ===")
    print("功能说明：")
    print("1. 读取GLSL着色器文件")
    print("2. 使用8个GLSL优化器进行优化")
    print("3. 自动检测着色器类型（vert/frag/comp等）")
    print("4. 输出优化后的代码到input.glsl")
    print(f"5. 使用malioc分析{gpu_target} GPU性能")
    print("6. 生成详细的分析报告")
    print()
    print("📝 使用说明：")
    print("  - 修改input_file变量指定GLSL文件路径")
    print("  - 修改gpu_target变量选择目标GPU")
    print("  - 确保已安装Mali Offline Compiler")
    print()
    
    # 运行测试
    success = tester.run_test(input_file)
    
    if success:
        print("\n✓ 测试脚本执行成功")
        print("📁 生成的文件:")
        print(f"  - {tester.original_file}: 原始GLSL代码")
        print(f"  - {tester.optimized_file}: 优化后的GLSL代码")
        print(f"  - {tester.comparison_file}: 性能对比分析报告")
    else:
        print("\n✗ 测试脚本执行失败")

if __name__ == "__main__":
    main()
