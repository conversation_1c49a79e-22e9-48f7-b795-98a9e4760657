# ===== 核心UI框架 =====
PyQt5>=5.15.0                    # 主要的GUI框架
pyqode.core>=3.0.0               # 代码编辑器核心组件
QScintilla>=2.11.0               # 语法高亮和代码编辑功能

# ===== 网络请求库 =====
requests>=2.25.0                 # HTTP请求库，用于Compiler Explorer API调用

# ===== 语法分析和编译器集成 =====
tree-sitter>=0.20.0              # 语法树解析库，用于tree_sitter_hlsl_optimizer.py
clang>=14.0.0                    # Clang Python绑定，用于clang_hlsl_optimizer.py (可选)

# ===== 数据处理和类型提示 =====
typing-extensions>=4.0.0         # 扩展类型提示支持，用于新的优化器类

# ===== 代码格式化库 =====
black>=22.0.0                   # 代码格式化工具 (主要)
autopep8>=1.7.0                 # 代码格式化工具 (备用)
yapf>=0.32.0                    # 代码格式化工具 (备用)

# ===== 开发和测试工具 =====
pytest>=6.0.0                   # 单元测试框架 (可选，用于测试优化器)

# ===== 系统工具依赖说明 =====
# 注意：以下工具需要单独安装，不能通过pip安装：
#
# 1. SPIR-V工具链 (用于spirvtools_hlsl_optimizer.py):
#    - glslangValidator: HLSL到SPIR-V编译器
#    - spirv-opt: SPIR-V优化器
#    - spirv-cross: SPIR-V到HLSL反编译器
#    下载地址: https://github.com/KhronosGroup/SPIRV-Tools/releases
#
# 2. Tree-sitter HLSL语法库 (用于tree_sitter_hlsl_optimizer.py):
#    需要手动编译tree-sitter-hlsl语法库为.so文件
#    参考: https://github.com/tree-sitter/tree-sitter
#
# 3. libclang (用于clang_hlsl_optimizer.py):
#    某些系统可能需要单独安装libclang库
#    Ubuntu: sudo apt-get install libclang-dev
#    Windows: 通过LLVM官方安装包安装