"""
HLSL向量化优化器 - 将标量操作转换为向量操作，提高并行度
"""
import re
from typing import Dict, List, Tuple, Set
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel

class HLSLVectorizationOptimizer(HLSLOptimizerBase):
    """
    向量化优化器，将标量操作转换为向量操作以提高并行度。
    
    支持的优化：
    - 标量运算向量化
    - 数组访问向量化
    - 函数调用向量化
    - 条件操作向量化
    - 循环向量化
    - SIMD指令生成
    
    用法：
        optimizer = VectorizationOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 向量化模式
        self.vectorization_patterns = {
            'scalar_ops': re.compile(r'(\w+)\s*([+\-*/])\s*(\w+)'),
            'array_access': re.compile(r'(\w+)\[([^\]]+)\]'),
            'function_call': re.compile(r'(\w+)\s*\(\s*([^)]+)\s*\)'),
            'assignment': re.compile(r'(\w+)\s*=\s*([^;]+);')
        }
        
        # 可向量化的函数
        self.vectorizable_functions = {
            'sin', 'cos', 'tan', 'asin', 'acos', 'atan',
            'exp', 'log', 'sqrt', 'rsqrt', 'abs', 'sign',
            'floor', 'ceil', 'round', 'frac', 'saturate',
            'min', 'max', 'clamp', 'lerp', 'smoothstep'
        }
    
    def optimize_code(self, code: str) -> str:
        """优化代码进行向量化"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._vectorize_scalar_operations(optimized_code)
            optimized_code = self._vectorize_array_access(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._vectorize_function_calls(optimized_code)
            optimized_code = self._vectorize_conditional_operations(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._vectorize_loops(optimized_code)
            optimized_code = self._generate_simd_instructions(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _vectorize_scalar_operations(self, code: str) -> str:
        """向量化标量运算"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 查找连续的相似标量运算
            if i + 1 < len(lines):
                current_op = self._extract_scalar_operation(line)
                next_op = self._extract_scalar_operation(lines[i + 1])
                
                if (current_op and next_op and 
                    self._can_vectorize_operations(current_op, next_op)):
                    
                    # 向量化两个操作
                    vectorized = self._create_vectorized_operation(current_op, next_op)
                    
                    if vectorized:
                        optimized_lines.append('    // Vectorized scalar operations')
                        optimized_lines.extend(vectorized)
                        self.statistics['optimizations_applied'] += 1
                        i += 2  # 跳过下一行
                        continue
            
            # 查找可以向量化的单个操作
            vectorized_single = self._vectorize_single_operation(line)
            if vectorized_single != line:
                optimized_lines.append('    // Vectorized operation')
                optimized_lines.append(vectorized_single)
                self.statistics['optimizations_applied'] += 1
            else:
                optimized_lines.append(line)
            
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _vectorize_array_access(self, code: str) -> str:
        """向量化数组访问"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 查找连续的数组访问
            array_accesses = re.findall(r'(\w+)\[([^\]]+)\]', line)
            
            if len(array_accesses) >= 2:
                # 检查是否可以向量化
                vectorizable_groups = self._group_vectorizable_accesses(array_accesses)
                
                if vectorizable_groups:
                    optimized_line = line
                    
                    for group in vectorizable_groups:
                        if len(group) >= 2:
                            # 创建向量化访问
                            vectorized_access = self._create_vectorized_access(group)
                            
                            # 替换原始访问
                            for array_name, index in group:
                                old_access = f"{array_name}[{index}]"
                                optimized_line = optimized_line.replace(old_access, vectorized_access, 1)
                            
                            self.statistics['optimizations_applied'] += 1
                    
                    optimized_lines.append(optimized_line)
                else:
                    optimized_lines.append(line)
            else:
                optimized_lines.append(line)
            
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _vectorize_function_calls(self, code: str) -> str:
        """向量化函数调用"""
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            optimized_line = line
            
            # 查找可向量化的函数调用
            for func_name in self.vectorizable_functions:
                pattern = re.compile(rf'\b{func_name}\s*\(\s*([^)]+)\s*\)')
                matches = list(pattern.finditer(line))
                
                if len(matches) >= 2:
                    # 检查参数是否可以向量化
                    args_list = [match.group(1) for match in matches]
                    
                    if self._can_vectorize_function_args(args_list):
                        # 创建向量化函数调用
                        vectorized_call = self._create_vectorized_function_call(func_name, args_list)
                        
                        # 替换原始调用
                        for match in reversed(matches):
                            optimized_line = (optimized_line[:match.start()] + 
                                            vectorized_call + 
                                            optimized_line[match.end():])
                        
                        self.statistics['optimizations_applied'] += 1
                        break
            
            optimized_lines.append(optimized_line)
        
        return '\n'.join(optimized_lines)
    
    def _vectorize_conditional_operations(self, code: str) -> str:
        """向量化条件操作"""
        optimizations = [
            # 条件赋值向量化
            # if (cond) a = x; if (cond) b = y; -> float2 temp = cond ? float2(x, y) : float2(a, b);
            
            # select/lerp 向量化
            (re.compile(r'(\w+)\s*=\s*([^?]+)\?\s*([^:]+):\s*([^;]+);\s*(\w+)\s*=\s*\2\?\s*([^:]+):\s*([^;]+);'),
             lambda m: self._vectorize_ternary_operations(m) if self._can_vectorize_ternary(m) else m.group(0)),
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            if callable(replacement):
                matches = list(pattern.finditer(optimized_code))
                for match in reversed(matches):
                    new_text = replacement(match)
                    if new_text != match.group(0):
                        optimized_code = optimized_code[:match.start()] + new_text + optimized_code[match.end():]
                        self.statistics['optimizations_applied'] += 1
            else:
                matches = len(pattern.findall(optimized_code))
                optimized_code = pattern.sub(replacement, optimized_code)
                self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _vectorize_loops(self, code: str) -> str:
        """向量化循环"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 查找简单的for循环
            for_match = re.search(r'for\s*\(\s*int\s+(\w+)\s*=\s*(\d+)\s*;\s*\1\s*<\s*(\d+)\s*;\s*\1\+\+\s*\)', line)
            
            if for_match:
                loop_var = for_match.group(1)
                start_val = int(for_match.group(2))
                end_val = int(for_match.group(3))
                iterations = end_val - start_val
                
                # 如果循环次数是2或4的倍数，可以考虑向量化
                if iterations % 4 == 0 and iterations <= 16:
                    loop_body = self._extract_loop_body(lines, i)
                    
                    if loop_body and self._can_vectorize_loop(loop_body, loop_var):
                        vectorized_loop = self._create_vectorized_loop(loop_body, loop_var, start_val, end_val)
                        
                        if vectorized_loop:
                            optimized_lines.append('    // Vectorized loop')
                            optimized_lines.extend(vectorized_loop)
                            self.statistics['optimizations_applied'] += 1
                            
                            # 跳过原始循环
                            i = self._find_loop_end(lines, i) + 1
                            continue
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _generate_simd_instructions(self, code: str) -> str:
        """生成SIMD指令提示"""
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            # 检查是否有可以使用SIMD的模式
            if any(pattern in line for pattern in ['float4', 'float3', 'float2']):
                # 检查向量运算
                if any(op in line for op in ['+', '-', '*', '/']):
                    optimized_lines.append('    // SIMD: Vector operation detected')
                    self.statistics['optimizations_applied'] += 1
                
                # 检查向量函数
                if any(func in line for func in self.vectorizable_functions):
                    optimized_lines.append('    // SIMD: Vectorizable function detected')
                    self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _extract_scalar_operation(self, line: str) -> Dict:
        """提取标量运算信息"""
        # 简化的实现
        assignment_match = re.search(r'(\w+)\s*=\s*(\w+)\s*([+\-*/])\s*(\w+)', line)
        
        if assignment_match:
            return {
                'result': assignment_match.group(1),
                'operand1': assignment_match.group(2),
                'operator': assignment_match.group(3),
                'operand2': assignment_match.group(4),
                'line': line
            }
        
        return None
    
    def _can_vectorize_operations(self, op1: Dict, op2: Dict) -> bool:
        """检查两个操作是否可以向量化"""
        return (op1['operator'] == op2['operator'] and
                op1['result'] != op2['result'] and
                op1['operand1'] != op2['operand1'])
    
    def _create_vectorized_operation(self, op1: Dict, op2: Dict) -> List[str]:
        """创建向量化操作"""
        operator = op1['operator']
        
        vectorized = [
            f"    float2 _vec_operand1 = float2({op1['operand1']}, {op2['operand1']});",
            f"    float2 _vec_operand2 = float2({op1['operand2']}, {op2['operand2']});",
            f"    float2 _vec_result = _vec_operand1 {operator} _vec_operand2;",
            f"    {op1['result']} = _vec_result.x;",
            f"    {op2['result']} = _vec_result.y;"
        ]
        
        return vectorized
    
    def _vectorize_single_operation(self, line: str) -> str:
        """向量化单个操作"""
        # 查找可以向量化的模式
        # 例如：a.x = b.x + c.x; a.y = b.y + c.y; -> a.xy = b.xy + c.xy;
        
        swizzle_pattern = re.search(r'(\w+)\.([xyzw])\s*=\s*(\w+)\.([xyzw])\s*([+\-*/])\s*(\w+)\.([xyzw])', line)
        
        if swizzle_pattern:
            var1, comp1, var2, comp2, op, var3, comp3 = swizzle_pattern.groups()
            
            if comp1 == comp2 == comp3:
                # 可以保持原样，或者提示可以向量化
                return f"    // Can be vectorized: {line.strip()}"
        
        return line
    
    def _group_vectorizable_accesses(self, accesses: List[Tuple[str, str]]) -> List[List[Tuple[str, str]]]:
        """将数组访问分组以便向量化"""
        groups = []
        current_group = []
        
        for array_name, index in accesses:
            if not current_group:
                current_group.append((array_name, index))
            elif array_name == current_group[0][0]:  # 同一数组
                current_group.append((array_name, index))
            else:
                if len(current_group) >= 2:
                    groups.append(current_group)
                current_group = [(array_name, index)]
        
        if len(current_group) >= 2:
            groups.append(current_group)
        
        return groups
    
    def _create_vectorized_access(self, group: List[Tuple[str, str]]) -> str:
        """创建向量化访问"""
        if len(group) == 2:
            array_name = group[0][0]
            indices = [access[1] for access in group]
            return f"{array_name}_vec2[{indices[0]}]"  # 简化的实现
        elif len(group) == 4:
            array_name = group[0][0]
            indices = [access[1] for access in group]
            return f"{array_name}_vec4[{indices[0]}]"  # 简化的实现
        
        return group[0][0] + "[" + group[0][1] + "]"
    
    def _can_vectorize_function_args(self, args_list: List[str]) -> bool:
        """检查函数参数是否可以向量化"""
        # 简化的实现：检查参数是否为标量
        return all(not any(vec_type in arg for vec_type in ['float2', 'float3', 'float4']) 
                  for arg in args_list)
    
    def _create_vectorized_function_call(self, func_name: str, args_list: List[str]) -> str:
        """创建向量化函数调用"""
        if len(args_list) == 2:
            return f"{func_name}(float2({args_list[0]}, {args_list[1]}))"
        elif len(args_list) == 4:
            return f"{func_name}(float4({', '.join(args_list)}))"
        
        return f"{func_name}({args_list[0]})"
    
    def _can_vectorize_ternary(self, match) -> bool:
        """检查三元操作是否可以向量化"""
        # 简化的实现
        return True
    
    def _vectorize_ternary_operations(self, match) -> str:
        """向量化三元操作"""
        var1, cond, true1, false1, var2, true2, false2 = match.groups()
        
        return (f"float2 _vec_result = {cond} ? float2({true1}, {true2}) : float2({false1}, {false2});\n"
                f"    {var1} = _vec_result.x;\n"
                f"    {var2} = _vec_result.y;")
    
    def _extract_loop_body(self, lines: List[str], start_idx: int) -> List[str]:
        """提取循环体"""
        # 简化的实现
        body = []
        brace_count = 0
        
        for i in range(start_idx, len(lines)):
            line = lines[i]
            if '{' in line:
                brace_count += line.count('{')
            if '}' in line:
                brace_count -= line.count('}')
            
            if i > start_idx:
                body.append(line)
            
            if brace_count == 0 and i > start_idx:
                break
        
        return body[:-1]  # 移除最后的 '}'
    
    def _can_vectorize_loop(self, loop_body: List[str], loop_var: str) -> bool:
        """检查循环是否可以向量化"""
        # 简化的实现：检查循环体是否包含简单的数组操作
        body_str = ' '.join(loop_body)
        return f'[{loop_var}]' in body_str and len(loop_body) < 5
    
    def _create_vectorized_loop(self, loop_body: List[str], loop_var: str, start: int, end: int) -> List[str]:
        """创建向量化循环"""
        # 简化的实现
        vectorized = [
            f"    // Vectorized loop: {end - start} iterations -> {(end - start) // 4} vector operations",
            f"    for (int {loop_var}_vec = {start}; {loop_var}_vec < {end}; {loop_var}_vec += 4) {{"
        ]
        
        for line in loop_body:
            # 替换标量操作为向量操作
            vectorized_line = line.replace(f'[{loop_var}]', f'_vec[{loop_var}_vec]')
            vectorized.append(vectorized_line)
        
        vectorized.append("    }")
        return vectorized
    
    def _find_loop_end(self, lines: List[str], start_idx: int) -> int:
        """找到循环结束位置"""
        brace_count = 0
        
        for i in range(start_idx, len(lines)):
            line = lines[i]
            if '{' in line:
                brace_count += line.count('{')
            if '}' in line:
                brace_count -= line.count('}')
            
            if brace_count == 0 and i > start_idx:
                return i
        
        return len(lines) - 1
