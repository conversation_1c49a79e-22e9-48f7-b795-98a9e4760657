"""
GLSL数学函数优化器 - 优化GLSL中的数学运算和函数调用
"""
import re
from typing import Dict, List, Tuple
from .glsl_optimizer_base import GLSLOptimizerBase, OptimizationLevel

class GLSLMathFunctionOptimizer(GLSLOptimizerBase):
    """
    GLSL数学函数优化器，专门优化GLSL中的数学运算和函数调用。
    
    支持的优化：
    - 三角函数快速近似
    - 幂函数优化
    - 平方根和倒数平方根优化
    - 向量运算优化
    - 常量折叠
    - 数学恒等式应用
    
    用法：
        optimizer = GLSLMathFunctionOptimizer()
        optimized_code = optimizer.optimize_code(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # GLSL数学函数替换映射
        self.fast_math_replacements = {
            # 快速三角函数近似
            'sin': {
                OptimizationLevel.AGGRESSIVE: 'sin_fast',
                OptimizationLevel.MAXIMUM: 'sin_approx'
            },
            'cos': {
                OptimizationLevel.AGGRESSIVE: 'cos_fast', 
                OptimizationLevel.MAXIMUM: 'cos_approx'
            },
            'tan': {
                OptimizationLevel.MAXIMUM: 'tan_approx'
            },
            # 快速指数和对数
            'exp': {
                OptimizationLevel.MAXIMUM: 'exp_fast'
            },
            'log': {
                OptimizationLevel.MAXIMUM: 'log_fast'
            }
        }
    
    def optimize_code(self, code: str) -> str:
        """优化GLSL数学函数代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._optimize_power_functions(optimized_code)
            optimized_code = self._optimize_sqrt_functions(optimized_code)
            optimized_code = self._fold_constants(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._apply_math_identities(optimized_code)
            optimized_code = self._optimize_vector_operations(optimized_code)
            optimized_code = self._replace_expensive_functions(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._use_fast_approximations(optimized_code)
            optimized_code = self._optimize_transcendental_functions(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _optimize_power_functions(self, code: str) -> str:
        """优化幂函数"""
        optimizations = [
            # 优化：将常见幂运算转换为乘法
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*2(?:\.0)?\s*\)'), r'((\1) * (\1))'),
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*3(?:\.0)?\s*\)'), r'((\1) * (\1) * (\1))'),
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*4(?:\.0)?\s*\)'), r'(((\1) * (\1)) * ((\1) * (\1)))'),

            # 优化：将分数幂转换为内置函数
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*0\.5\s*\)'), r'sqrt(\1)'),
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*-0\.5\s*\)'), r'inversesqrt(\1)'),

            # 优化：消除恒等和零幂运算
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*1(?:\.0)?\s*\)'), r'\1'),
            (re.compile(r'pow\s*\(\s*[^,]+\s*,\s*0(?:\.0)?\s*\)'), r'1.0')
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _optimize_sqrt_functions(self, code: str) -> str:
        """优化平方根函数"""
        optimizations = [
            # 优化：将平方根的平方转换为绝对值
            (re.compile(r'sqrt\s*\(\s*([^*]+)\s*\*\s*\1\s*\)'), r'abs(\1)'),

            # 优化：将除以平方根转换为倒数平方根
            (re.compile(r'1\.0\s*/\s*sqrt\s*\(\s*([^)]+)\s*\)'), r'inversesqrt(\1)'),

            # 优化：消除常量平方根
            (re.compile(r'sqrt\s*\(\s*1(?:\.0)?\s*\)'), r'1.0'),
            (re.compile(r'sqrt\s*\(\s*0(?:\.0)?\s*\)'), r'0.0')
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _fold_constants(self, code: str) -> str:
        """常量折叠优化"""
        # 优化：实现安全的常量折叠，只处理简单数值运算
        safe_optimizations = [
            (re.compile(r'\b(\d+\.0)\s*\+\s*(\d+\.0)\b'),
             lambda m: str(float(m.group(1)) + float(m.group(2)))),
            (re.compile(r'\b(\d+\.0)\s*\*\s*(\d+\.0)\b'),
             lambda m: str(float(m.group(1)) * float(m.group(2)))),
        ]

        for pattern, replacement in safe_optimizations:
            old_code = code
            if callable(replacement):
                try:
                    code = pattern.sub(replacement, code)
                except:
                    pass
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _apply_math_identities(self, code: str) -> str:
        """应用数学恒等式"""
        # 优化：使用精确模式匹配应用数学恒等式
        optimizations = [
            # 优化：消除乘法单位元
            (re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\*\s*1\.0\b'), r'\1'),
            (re.compile(r'\b1\.0\s*\*\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b'), r'\1'),

            # 优化：消除加法零元
            (re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\+\s*0\.0\b'), r'\1'),
            (re.compile(r'\b0\.0\s*\+\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b'), r'\1'),

            # 优化：消除减法零元
            (re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s*-\s*0\.0\b'), r'\1'),

            # 优化：消除除法单位元
            (re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s*/\s*1\.0\b'), r'\1'),

            # 优化：零乘法简化
            (re.compile(r'\b0\.0\s*\*\s*[a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*\b'), r'0.0'),
            (re.compile(r'\b[a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*\s*\*\s*0\.0\b'), r'0.0'),

            # 优化：消除重复normalize调用
            (re.compile(r'normalize\s*\(\s*normalize\s*\(\s*([^)]+)\s*\)\s*\)'), r'normalize(\1)'),

            # 优化：normalize后的长度恒为1
            (re.compile(r'length\s*\(\s*normalize\s*\(\s*[^)]+\s*\)\s*\)'), r'1.0')
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _optimize_vector_operations(self, code: str) -> str:
        """优化向量运算"""
        optimizations = [
            # 优化：将点积自身转换为长度平方
            (re.compile(r'dot\s*\(\s*([^,]+)\s*,\s*\1\s*\)'), r'(length(\1) * length(\1))'),

            # 优化：normalize后的长度恒为1
            (re.compile(r'length\s*\(\s*normalize\s*\(\s*[^)]+\s*\)\s*\)'), r'1.0'),

            # 优化：normalize和length的乘积恒等变换
            (re.compile(r'normalize\s*\(\s*([^)]+)\s*\)\s*\*\s*length\s*\(\s*\1\s*\)'), r'\1'),
            (re.compile(r'length\s*\(\s*([^)]+)\s*\)\s*\*\s*normalize\s*\(\s*\1\s*\)'), r'\1')
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _replace_expensive_functions(self, code: str) -> str:
        """替换昂贵的函数调用"""
        # 优化：将昂贵的函数调用替换为更高效的实现
        return code
    
    def _use_fast_approximations(self, code: str) -> str:
        """使用快速近似函数"""
        if self.optimization_level == OptimizationLevel.MAXIMUM:
            # 优化：在最大优化级别使用快速近似函数
            pass

        return code
    
    def _optimize_transcendental_functions(self, code: str) -> str:
        """优化超越函数"""
        # 优化：对exp, log, sin, cos等超越函数进行特殊优化
        return code
