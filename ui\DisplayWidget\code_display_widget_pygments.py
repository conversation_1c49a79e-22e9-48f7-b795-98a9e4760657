from PyQt5.QtWidgets import QVBoxLayout, QLabel, QTextEdit, QScrollBar, QShortcut
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QSyntaxHighlighter, QTextCharFormat, QColor, QFont, QKeySequence
import re
from .text_display_widget_base import DisplayBaseWidget

try:
    from pygments import highlight
    from pygments.lexers import get_lexer_by_name, guess_lexer
    from pygments.formatters import get_formatter_by_name
    from pygments.styles import get_style_by_name
    PYGMENTS_AVAILABLE = True
except ImportError:
    PYGMENTS_AVAILABLE = False

class PygmentsSyntaxHighlighter(QSyntaxHighlighter):
    """基于Pygments的语法高亮器"""
    def __init__(self, document, language='hlsl', style='default'):
        super().__init__(document)
        self.language = language
        self.style_name = style
        self._setup_formats()
    def _setup_formats(self):
        if not PYGMENTS_AVAILABLE:
            return
        try:
            style = get_style_by_name(self.style_name)
            self.formats = {}
            for token, style_dict in style.list_styles():
                format = QTextCharFormat()
                if style_dict['color']:
                    format.setForeground(QColor('#' + style_dict['color']))
                if style_dict['bgcolor']:
                    format.setBackground(QColor('#' + style_dict['bgcolor']))
                if style_dict['bold']:
                    format.setFontWeight(QFont.Bold)
                if style_dict['italic']:
                    format.setFontItalic(True)
                if style_dict['underline']:
                    format.setFontUnderline(True)
                self.formats[token] = format
        except:
            self.formats = {}
    def highlightBlock(self, text):
        if not PYGMENTS_AVAILABLE or not self.formats:
            return
        try:
            lexer = get_lexer_by_name(self.language)
            tokens = list(lexer.get_tokens(text))
            index = 0
            for token_type, token_text in tokens:
                length = len(token_text)
                format = None
                for token_key, token_format in self.formats.items():
                    if token_type in token_key or str(token_type).startswith(str(token_key)):
                        format = token_format
                        break
                if format:
                    self.setFormat(index, length, format)
                index += length
        except:
            pass

class CodeDisplayWidget(DisplayBaseWidget):
    """使用Pygments的单区域代码显示组件，支持语法高亮"""
    def __init__(self, parent=None, model='Pygments', source=None):
        super().__init__(parent, model=model, source=source)
        layout = QVBoxLayout(self)
        self.text = QTextEdit()
        self._setup_text_edit(self.text)
        layout.addWidget(self.text)
        self.setLayout(layout)
        self.highlighter = None
        self.set_language('hlsl')

        # 设置快捷键
        self._setup_shortcuts()

        # 连接文本变化信号
        self.text.textChanged.connect(self._on_text_changed)
    
    def _setup_text_edit(self, text_edit):
        text_edit.setReadOnly(False)  # 默认设置为可编辑
        text_edit.setLineWrapMode(QTextEdit.NoWrap)
        text_edit.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        text_edit.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        font = QFont('Consolas', 10)
        font.setFixedPitch(True)
        text_edit.setFont(font)
        # 移除滚动条 setStyleSheet，保持和 QScintilla 一致
        # VSCode风格：深色背景、浅色前景、选区、边框
        text_edit.setStyleSheet('''
            background-color: #1e1e1e;
            color: #d4d4d4;
            border: 1px solid #23272e;
            selection-background-color: #264f78;
            selection-color: #fff;
        ''')

    def _setup_shortcuts(self):
        """设置快捷键"""
        # 不在这里设置快捷键，避免冲突
        # 快捷键将由主窗口统一管理
        pass

    def _on_text_changed(self):
        """文本变化时的处理"""
        if not self.is_modified:  # 只有在未修改状态时才设置为已修改
            self.set_modified(True)

    def set_language(self, language):
        std_lang = super().set_language(language)
        if PYGMENTS_AVAILABLE:
            if self.highlighter:
                self.highlighter.deleteLater()
            try:
                self.highlighter = PygmentsSyntaxHighlighter(self.text.document(), std_lang)
            except:
                self.highlighter = None

    def set_text(self, text):
        self.text.setPlainText(text)
        self.set_modified(False)

    def clear_text(self):
        self.text.clear()
        self.set_modified(False)

    def set_label(self, label):
        pass

    def get_text(self):
        return self.text.toPlainText()

    def set_editable(self, editable=True):
        """设置是否可编辑"""
        self.text.setReadOnly(not editable)
