"""
基础着色器优化器模块

包含HLSL和GLSL优化器基类和专业优化器：

HLSL优化器：
- HLSLOptimizerBase: HLSL优化器基类
- HLSLTextureSamplingOptimizer: HLSL纹理采样优化器
- HLSLLoopUnrollOptimizer: HLSL循环展开优化器
- HLSLBranchOptimizer: HLSL分支优化器
- HLSLMemoryAccessOptimizer: HLSL内存访问优化器
- HLSLMathFunctionOptimizer: HLSL数学函数优化器
- HLSLVectorizationOptimizer: HLSL向量化优化器
- HLSLPrecisionAnalyzer: HLSL精度分析优化器
- HLSLRegisterAllocationOptimizer: HLSL寄存器分配优化器

GLSL优化器：
- GLSLOptimizerBase: GLSL优化器基类
- GLSLTextureSamplingOptimizer: GLSL纹理采样优化器
- GLSLLoopUnrollOptimizer: GLSL循环展开优化器
- GLSLBranchOptimizer: GLSL分支优化器
- GLSLMemoryAccessOptimizer: GLSL内存访问优化器
- GLSLMathFunctionOptimizer: GLSL数学函数优化器
- GLSLVectorizationOptimizer: GLSL向量化优化器
- GLSLPrecisionAnalyzer: GLSL精度分析优化器
- GLSLRegisterAllocationOptimizer: GLSL寄存器分配优化器

- OptimizationLevel: 优化级别枚举
"""

# HLSL优化器导入
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel
from .hlsl_texture_sampling_optimizer import HLSLTextureSamplingOptimizer
from .hlsl_loop_unroll_optimizer import HLSLLoopUnrollOptimizer
from .hlsl_branch_optimizer import HLSLBranchOptimizer
from .hlsl_memory_access_optimizer import HLSLMemoryAccessOptimizer
from .hlsl_math_function_optimizer import HLSLMathFunctionOptimizer
from .hlsl_vectorization_optimizer import HLSLVectorizationOptimizer
from .hlsl_precision_analyzer import HLSLPrecisionAnalyzer
from .hlsl_register_allocation_optimizer import HLSLRegisterAllocationOptimizer

# GLSL优化器导入
from .glsl_optimizer_base import GLSLOptimizerBase
from .glsl_texture_sampling_optimizer import GLSLTextureSamplingOptimizer
from .glsl_loop_unroll_optimizer import GLSLLoopUnrollOptimizer
from .glsl_branch_optimizer import GLSLBranchOptimizer
from .glsl_memory_access_optimizer import GLSLMemoryAccessOptimizer
from .glsl_math_function_optimizer import GLSLMathFunctionOptimizer
from .glsl_vectorization_optimizer import GLSLVectorizationOptimizer
from .glsl_precision_analyzer import GLSLPrecisionAnalyzer
from .glsl_register_allocation_optimizer import GLSLRegisterAllocationOptimizer

# 优化器管理器导入
from .hlsl_optimizer_manager import HLSLOptimizerManager
from .glsl_optimizer_manager import GLSLOptimizerManager

__all__ = [
    # 基础类和枚举
    'OptimizationLevel',

    # HLSL优化器
    'HLSLOptimizerBase',
    'HLSLTextureSamplingOptimizer',
    'HLSLLoopUnrollOptimizer',
    'HLSLBranchOptimizer',
    'HLSLMemoryAccessOptimizer',
    'HLSLMathFunctionOptimizer',
    'HLSLVectorizationOptimizer',
    'HLSLPrecisionAnalyzer',
    'HLSLRegisterAllocationOptimizer',

    # GLSL优化器
    'GLSLOptimizerBase',
    'GLSLTextureSamplingOptimizer',
    'GLSLLoopUnrollOptimizer',
    'GLSLBranchOptimizer',
    'GLSLMemoryAccessOptimizer',
    'GLSLMathFunctionOptimizer',
    'GLSLVectorizationOptimizer',
    'GLSLPrecisionAnalyzer',
    'GLSLRegisterAllocationOptimizer',

    # 优化器管理器
    'HLSLOptimizerManager',
    'GLSLOptimizerManager'
]
