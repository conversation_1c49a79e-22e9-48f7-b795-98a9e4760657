"""
GLSL纹理采样优化器 - 优化GLSL中的纹理采样操作
"""
import re
from typing import Dict, List, Tuple, Set
from .glsl_optimizer_base import GLSLOptimizerBase, OptimizationLevel

class GLSLTextureSamplingOptimizer(GLSLOptimizerBase):
    """
    GLSL纹理采样优化器，专门优化GLSL中的纹理采样操作。
    
    支持的优化：
    - 纹理采样函数优化
    - LOD计算优化
    - 纹理坐标优化
    - 重复采样消除
    - 纹理缓存优化
    - 采样精度优化
    
    用法：
        optimizer = GLSLTextureSamplingOptimizer()
        optimized_code = optimizer.optimize_code(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # GLSL纹理采样函数
        self.texture_functions = {
            'texture': {'cost': 8, 'alternatives': ['textureLod']},
            'textureLod': {'cost': 10, 'alternatives': []},
            'textureGrad': {'cost': 12, 'alternatives': ['textureLod']},
            'textureProj': {'cost': 10, 'alternatives': ['texture']},
            'texelFetch': {'cost': 4, 'alternatives': []},
            'textureGather': {'cost': 6, 'alternatives': []},
            'textureSize': {'cost': 2, 'alternatives': []},
            'textureQueryLod': {'cost': 4, 'alternatives': []},
            'textureQueryLevels': {'cost': 2, 'alternatives': []}
        }
        
        # 纹理类型
        self.texture_types = [
            'sampler2D', 'sampler3D', 'samplerCube', 'sampler2DArray',
            'samplerCubeArray', 'sampler2DShadow', 'samplerCubeShadow',
            'sampler2DArrayShadow', 'samplerCubeArrayShadow',
            'isampler2D', 'isampler3D', 'isamplerCube',
            'usampler2D', 'usampler3D', 'usamplerCube'
        ]
    
    def optimize_code(self, code: str) -> str:
        """优化GLSL纹理采样代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._optimize_texture_coordinates(optimized_code)
            optimized_code = self._eliminate_redundant_samples(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._optimize_lod_calculations(optimized_code)
            optimized_code = self._optimize_texture_functions(optimized_code)
            optimized_code = self._cache_texture_samples(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._optimize_texture_precision(optimized_code)
            optimized_code = self._suggest_texture_format_optimizations(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _optimize_texture_coordinates(self, code: str) -> str:
        """优化纹理坐标"""
        optimizations = [
            # 优化：简化冗余的向量构造
            (re.compile(r'vec2\s*\(\s*(\w+)\.x\s*,\s*\1\.y\s*\)'), r'\1'),
            (re.compile(r'vec3\s*\(\s*(\w+)\.xy\s*,\s*([^)]+)\s*\)'), r'vec3(\1, \2)'),

            # 优化：消除恒等变换
            (re.compile(r'(\w+)\s*\*\s*vec2\s*\(\s*1\.0\s*,\s*1\.0\s*\)'), r'\1'),
            (re.compile(r'(\w+)\s*\+\s*vec2\s*\(\s*0\.0\s*,\s*0\.0\s*\)'), r'\1'),
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _eliminate_redundant_samples(self, code: str) -> str:
        """消除重复的纹理采样"""
        lines = code.splitlines()
        texture_samples = {}
        optimized_lines = []

        for line in lines:
            # 优化：检测并缓存重复的纹理采样操作
            texture_match = re.search(r'texture\s*\(\s*(\w+)\s*,\s*([^)]+)\s*\)', line)
            if texture_match:
                sampler = texture_match.group(1)
                coords = texture_match.group(2)
                sample_key = f"{sampler}_{coords}"

                if sample_key in texture_samples:
                    # 优化：使用已缓存的采样结果
                    cached_var = texture_samples[sample_key]
                    new_line = line.replace(texture_match.group(0), cached_var)
                    optimized_lines.append(new_line)
                    self.statistics['optimizations_applied'] += 1
                else:
                    # 优化：创建新的采样缓存
                    if '=' in line:
                        var_match = re.match(r'\s*(\w+)\s*=', line.strip())
                        if var_match:
                            texture_samples[sample_key] = var_match.group(1)
                    else:
                        temp_var = f"texSample_{len(texture_samples)}"
                        texture_samples[sample_key] = temp_var
                        optimized_lines.append(f"vec4 {temp_var} = {texture_match.group(0)};")
                        new_line = line.replace(texture_match.group(0), temp_var)
                        optimized_lines.append(new_line)
                        continue

                    optimized_lines.append(line)
            else:
                optimized_lines.append(line)

        return '\n'.join(optimized_lines)
    
    def _optimize_lod_calculations(self, code: str) -> str:
        """优化LOD计算"""
        optimizations = [
            # 优化：将零LOD的textureLod调用简化为texture调用
            (re.compile(r'textureLod\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*0\.0\s*\)'), r'texture(\1, \2)'),
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _optimize_texture_functions(self, code: str) -> str:
        """优化纹理函数选择"""
        optimizations = [
            # 优化：将单位投影的textureProj简化为texture调用
            (re.compile(r'textureProj\s*\(\s*([^,]+)\s*,\s*vec3\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*1\.0\s*\)\s*\)'),
             r'texture(\1, vec2(\2, \3))'),
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _cache_texture_samples(self, code: str) -> str:
        """缓存纹理采样结果"""
        # 优化：分析循环中的纹理采样模式
        lines = code.splitlines()
        in_loop = False
        loop_samples = set()

        for line in lines:
            if re.search(r'\b(for|while)\b', line):
                in_loop = True
            elif '}' in line and in_loop:
                in_loop = False
            elif in_loop:
                # 优化：检测循环内的纹理采样操作
                texture_matches = re.findall(r'texture\s*\(\s*(\w+)\s*,\s*([^)]+)\s*\)', line)
                for match in texture_matches:
                    loop_samples.add(f"{match[0]}_{match[1]}")

        # 优化：为循环中的重复采样添加缓存建议
        if loop_samples:
            suggestion = "// 建议：考虑将循环中的纹理采样移到循环外进行缓存\n"
            code = suggestion + code
            self.statistics['optimizations_applied'] += 1

        return code
    
    def _optimize_texture_precision(self, code: str) -> str:
        """优化纹理采样精度"""
        optimizations = [
            # 优化：降低不必要的高精度纹理采样
            (re.compile(r'(highp|mediump)\s+(texture\s*\([^)]+\))'), r'lowp \2'),

            # 优化：为颜色纹理使用中等精度
            (re.compile(r'highp\s+(vec[34])\s+(\w*[Cc]olor\w*)\s*=\s*texture'), r'mediump \1 \2 = texture'),
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _suggest_texture_format_optimizations(self, code: str) -> str:
        """建议纹理格式优化"""
        suggestions = []

        # 优化：分析纹理通道使用模式并提供格式建议
        if 'texture(' in code and '.rgb' in code:
            suggestions.append('// 建议：如果只使用RGB通道，考虑使用RGB格式纹理而非RGBA')

        if 'texture(' in code and '.a' in code and '.rgb' not in code:
            suggestions.append('// 建议：如果只使用Alpha通道，考虑使用单通道纹理')

        if re.search(r'texture\s*\([^)]+\)\s*\.x', code):
            suggestions.append('// 建议：如果只使用单个通道，考虑使用R8格式纹理')

        # 优化：检查精度需求并提供建议
        if 'highp' in code and 'texture' in code:
            suggestions.append('// 建议：检查是否真的需要高精度纹理，mediump可能足够')

        if suggestions:
            return '\n'.join(suggestions) + '\n' + code

        return code
    
    def _analyze_texture_usage_patterns(self, code: str) -> Dict[str, List[str]]:
        """分析纹理使用模式"""
        patterns = {
            'samplers': [],
            'coordinates': [],
            'functions': [],
            'swizzles': []
        }
        
        # 查找采样器声明
        sampler_matches = re.findall(r'uniform\s+(\w*sampler\w*)\s+(\w+)', code)
        patterns['samplers'] = [match[1] for match in sampler_matches]
        
        # 查找纹理函数调用
        function_matches = re.findall(r'(texture\w*)\s*\(', code)
        patterns['functions'] = list(set(function_matches))
        
        # 查找swizzle操作
        swizzle_matches = re.findall(r'texture[^.]*\.([xyzwrgba]+)', code)
        patterns['swizzles'] = list(set(swizzle_matches))
        
        return patterns
    
    def _estimate_texture_cost(self, code: str) -> int:
        """估算纹理采样成本"""
        total_cost = 0
        
        for func_name, func_info in self.texture_functions.items():
            count = len(re.findall(rf'\b{func_name}\s*\(', code))
            total_cost += count * func_info['cost']
        
        return total_cost
