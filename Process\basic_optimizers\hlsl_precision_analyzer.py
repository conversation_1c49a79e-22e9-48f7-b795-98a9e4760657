"""
HLSL精度分析优化器 - 智能分析变量精度需求，自动选择最优的数据类型
"""
import re
from typing import Dict, List, Tuple, Set, Optional
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel

class HLSLPrecisionAnalyzer(HLSLOptimizerBase):
    """
    精度分析优化器，智能分析变量的精度需求并自动选择最优的数据类型。
    
    支持的优化：
    - 自动精度降级（float -> half）
    - 精度升级建议（half -> float）
    - 整数类型优化（int -> uint/min16int）
    - 精度传播分析
    - 数值范围分析
    - 精度损失评估
    
    用法：
        analyzer = PrecisionAnalyzer()
        optimized_code = analyzer.optimize_code(hlsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 精度要求分类
        self.precision_requirements = {
            'high_precision': {
                'keywords': ['world', 'position', 'depth', 'z', 'coord', 'matrix', 'transform'],
                'operations': ['mul', 'dot', 'cross', 'normalize'],
                'contexts': ['vertex_shader', 'geometry_processing']
            },
            'medium_precision': {
                'keywords': ['normal', 'tangent', 'binormal', 'direction'],
                'operations': ['lerp', 'smoothstep', 'clamp'],
                'contexts': ['lighting', 'shading']
            },
            'low_precision': {
                'keywords': ['color', 'colour', 'uv', 'texcoord', 'alpha', 'opacity'],
                'operations': ['saturate', 'abs', 'sign'],
                'contexts': ['pixel_shader', 'color_processing']
            }
        }
        
        # 类型映射
        self.type_mappings = {
            'float': {'half': 2, 'min16float': 2},
            'float2': {'half2': 2, 'min16float2': 2},
            'float3': {'half3': 2, 'min16float3': 2},
            'float4': {'half4': 2, 'min16float4': 2},
            'int': {'uint': 4, 'min16int': 2, 'min16uint': 2},
            'int2': {'uint2': 4, 'min16int2': 2},
            'int3': {'uint3': 4, 'min16int3': 2},
            'int4': {'uint4': 4, 'min16int4': 2}
        }
        
        # 变量分析结果
        self.variable_analysis = {}
    
    def optimize_code(self, code: str) -> str:
        """分析并优化代码精度"""
        self.reset_statistics()
        
        # 分析变量精度需求
        self._analyze_precision_requirements(code)
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._apply_basic_precision_rules(optimized_code)
            optimized_code = self._optimize_integer_types(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._propagate_precision_requirements(optimized_code)
            optimized_code = self._analyze_numerical_ranges(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._perform_precision_optimization(optimized_code)
            optimized_code = self._add_precision_annotations(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _analyze_precision_requirements(self, code: str):
        """分析变量的精度需求"""
        lines = code.splitlines()
        
        # 提取所有变量声明
        for line_num, line in enumerate(lines):
            var_decl = re.search(r'\b(float|half|int|uint|min16float|min16int|min16uint)(\d*(?:x\d+)?)\s+([a-zA-Z_][a-zA-Z0-9_]*)', line)
            
            if var_decl:
                var_type = var_decl.group(1) + (var_decl.group(2) or '')
                var_name = var_decl.group(3)
                
                # 分析变量名称暗示的精度需求
                precision_hint = self._analyze_variable_name(var_name)
                
                # 分析使用上下文
                usage_context = self._analyze_usage_context(code, var_name)
                
                # 分析数值操作
                operations = self._analyze_variable_operations(code, var_name)
                
                self.variable_analysis[var_name] = {
                    'declared_type': var_type,
                    'line': line_num,
                    'precision_hint': precision_hint,
                    'usage_context': usage_context,
                    'operations': operations,
                    'recommended_type': None,
                    'confidence': 0.0
                }
        
        # 计算推荐类型
        self._compute_recommended_types()
    
    def _analyze_variable_name(self, var_name: str) -> str:
        """根据变量名分析精度需求"""
        var_lower = var_name.lower()
        
        for precision, requirements in self.precision_requirements.items():
            for keyword in requirements['keywords']:
                if keyword in var_lower:
                    return precision
        
        return 'medium_precision'  # 默认中等精度
    
    def _analyze_usage_context(self, code: str, var_name: str) -> List[str]:
        """分析变量的使用上下文"""
        contexts = []
        
        # 查找变量在哪些函数中使用
        functions = self.extract_functions(code)
        
        for func_name, func_body, _, _ in functions:
            if var_name in func_body:
                # 根据函数名推断上下文
                func_lower = func_name.lower()
                
                if any(vs_hint in func_lower for vs_hint in ['vs', 'vertex', 'vert']):
                    contexts.append('vertex_shader')
                elif any(ps_hint in func_lower for ps_hint in ['ps', 'pixel', 'frag']):
                    contexts.append('pixel_shader')
                elif any(cs_hint in func_lower for cs_hint in ['cs', 'compute']):
                    contexts.append('compute_shader')
                
                if any(light_hint in func_lower for light_hint in ['light', 'shade', 'illuminate']):
                    contexts.append('lighting')
                elif any(color_hint in func_lower for color_hint in ['color', 'colour']):
                    contexts.append('color_processing')
        
        return contexts
    
    def _analyze_variable_operations(self, code: str, var_name: str) -> List[str]:
        """分析变量参与的操作"""
        operations = []
        
        # 查找变量参与的数学操作
        lines = code.splitlines()
        
        for line in lines:
            if var_name in line:
                # 查找数学函数
                for precision, requirements in self.precision_requirements.items():
                    for operation in requirements['operations']:
                        if operation in line:
                            operations.append(operation)
                
                # 查找基础运算
                if any(op in line for op in ['+', '-', '*', '/']):
                    operations.append('arithmetic')
                
                # 查找比较操作
                if any(op in line for op in ['<', '>', '==', '!=']):
                    operations.append('comparison')
                
                # 查找赋值操作
                if f'{var_name} =' in line:
                    operations.append('assignment')
        
        return list(set(operations))  # 去重
    
    def _compute_recommended_types(self):
        """计算推荐的数据类型"""
        for var_name, analysis in self.variable_analysis.items():
            current_type = analysis['declared_type']
            precision_hint = analysis['precision_hint']
            contexts = analysis['usage_context']
            operations = analysis['operations']
            
            # 计算精度需求分数
            precision_score = 0.0
            
            # 基于变量名的分数
            if precision_hint == 'high_precision':
                precision_score += 3.0
            elif precision_hint == 'medium_precision':
                precision_score += 2.0
            else:  # low_precision
                precision_score += 1.0
            
            # 基于上下文的分数
            if 'vertex_shader' in contexts or 'geometry_processing' in contexts:
                precision_score += 2.0
            elif 'lighting' in contexts:
                precision_score += 1.0
            elif 'color_processing' in contexts:
                precision_score -= 1.0
            
            # 基于操作的分数
            high_precision_ops = ['mul', 'dot', 'cross', 'normalize']
            if any(op in operations for op in high_precision_ops):
                precision_score += 1.5
            
            # 推荐类型
            if current_type.startswith('float'):
                if precision_score < 2.0:
                    # 可以降级到half
                    recommended = current_type.replace('float', 'half')
                    analysis['recommended_type'] = recommended
                    analysis['confidence'] = min(0.9, (2.0 - precision_score) / 2.0)
                elif precision_score > 4.0:
                    # 保持float
                    analysis['recommended_type'] = current_type
                    analysis['confidence'] = 0.8
            
            elif current_type.startswith('half'):
                if precision_score > 3.5:
                    # 建议升级到float
                    recommended = current_type.replace('half', 'float')
                    analysis['recommended_type'] = recommended
                    analysis['confidence'] = min(0.9, (precision_score - 3.5) / 2.0)
    
    def _apply_basic_precision_rules(self, code: str) -> str:
        """应用基础精度规则"""
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            optimized_line = line
            
            # 应用变量分析结果
            for var_name, analysis in self.variable_analysis.items():
                if (analysis['recommended_type'] and 
                    analysis['recommended_type'] != analysis['declared_type'] and
                    analysis['confidence'] > 0.7):
                    
                    # 替换类型声明
                    old_decl = f"{analysis['declared_type']} {var_name}"
                    new_decl = f"{analysis['recommended_type']} {var_name}"
                    
                    if old_decl in line:
                        optimized_line = line.replace(old_decl, new_decl)
                        self.statistics['optimizations_applied'] += 1
                        
                        # 移除调试注释
            
            optimized_lines.append(optimized_line)
        
        return '\n'.join(optimized_lines)
    
    def _optimize_integer_types(self, code: str) -> str:
        """优化整数类型"""
        optimizations = [
            # 循环计数器使用min16int
            (re.compile(r'for\s*\(\s*int\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*='),
             r'for (min16int \1 ='),
            
            # 数组索引使用uint（当确定非负时）
            (re.compile(r'int\s+([a-zA-Z_][a-zA-Z0-9_]*index[a-zA-Z0-9_]*)'),
             r'uint \1'),
            
            # 位操作使用uint
            (re.compile(r'int\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]*[&|^~]'),
             r'uint \1 ='),
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            if matches > 0:
                optimized_code = pattern.sub(replacement, optimized_code)
                self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _propagate_precision_requirements(self, code: str) -> str:
        """传播精度需求"""
        lines = code.splitlines()
        optimized_lines = []
        
        # 分析精度传播
        precision_graph = self._build_precision_graph(code)
        
        for line in lines:
            optimized_line = line
            
            # 检查赋值操作的精度传播
            assignment = re.search(r'(\w+)\s*=\s*([^;]+);', line)
            if assignment:
                target_var = assignment.group(1)
                expression = assignment.group(2)
                
                # 分析表达式中的变量精度
                expr_vars = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', expression)
                
                if target_var in self.variable_analysis:
                    target_analysis = self.variable_analysis[target_var]
                    
                    # 检查是否需要精度转换
                    for var in expr_vars:
                        if var in self.variable_analysis:
                            source_analysis = self.variable_analysis[var]
                            
                            if (source_analysis['recommended_type'] and
                                target_analysis['recommended_type'] and
                                source_analysis['recommended_type'] != target_analysis['recommended_type']):

                                # 移除精度不匹配的调试注释
                                pass
            
            optimized_lines.append(optimized_line)
        
        return '\n'.join(optimized_lines)
    
    def _analyze_numerical_ranges(self, code: str) -> str:
        """分析数值范围"""
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            # 查找常量赋值
            const_assignment = re.search(r'(\w+)\s*=\s*([\d.]+)', line)
            
            if const_assignment:
                var_name = const_assignment.group(1)
                value = float(const_assignment.group(2))
                
                # 分析数值范围
                if var_name in self.variable_analysis:
                    analysis = self.variable_analysis[var_name]
                    
                    # 检查是否可以使用更小的类型（移除调试注释）
                    if analysis['declared_type'].startswith('float'):
                        if -65504 <= value <= 65504:  # half精度范围
                            self.statistics['optimizations_applied'] += 1

                    elif analysis['declared_type'].startswith('int'):
                        if 0 <= value <= 65535:  # uint16范围
                            self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _perform_precision_optimization(self, code: str) -> str:
        """执行精度优化"""
        optimized_code = code
        
        # 应用高置信度的类型更改
        for var_name, analysis in self.variable_analysis.items():
            if (analysis['recommended_type'] and 
                analysis['confidence'] > 0.8 and
                analysis['recommended_type'] != analysis['declared_type']):
                
                old_type = analysis['declared_type']
                new_type = analysis['recommended_type']
                
                # 全局替换类型
                pattern = re.compile(rf'\b{old_type}\s+{var_name}\b')
                optimized_code = pattern.sub(f'{new_type} {var_name}', optimized_code)
                
                self.statistics['optimizations_applied'] += 1
        
        return optimized_code
    
    def _add_precision_annotations(self, code: str) -> str:
        """添加精度注释"""
        lines = code.splitlines()
        optimized_lines = []
        
        # 移除精度分析摘要注释，直接处理代码
        optimized_lines.extend(lines)
        
        return '\n'.join(optimized_lines)
    
    def _build_precision_graph(self, code: str) -> Dict:
        """构建精度依赖图"""
        # 简化的实现
        graph = {}
        
        for var_name in self.variable_analysis:
            graph[var_name] = {
                'depends_on': [],
                'used_by': []
            }
        
        return graph
