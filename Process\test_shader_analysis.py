#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器代码行分析测试工具
用于测试和调试代码行分析功能
"""

import sys
import os
import webbrowser
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from Process.Analysis import ShaderAnalysisProcessor

class ShaderAnalysisTest:
    """着色器分析测试类"""
    
    def __init__(self):
        self.processor = ShaderAnalysisProcessor()
        self.project_root = Path(__file__).parent.parent
    
    def test_shader_analysis(self, shader_code: str = None, input_file: str = None, 
                           output_name: str = "test_analysis", auto_open: bool = True):
        """
        测试着色器分析
        
        Args:
            shader_code: 着色器代码字符串
            input_file: 输入文件路径（如果为空则使用shader_code）
            output_name: 输出文件名前缀
            auto_open: 是否自动打开HTML文件
        """
        
        # 获取着色器代码
        if input_file and os.path.exists(input_file):
            print(f"📁 从文件读取着色器代码: {input_file}")
            with open(input_file, 'r', encoding='utf-8') as f:
                shader_content = f.read()
        elif shader_code:
            print("📝 使用提供的着色器代码字符串")
            shader_content = shader_code
        else:
            print("❌ 错误：未提供着色器代码或文件路径")
            return None
        
        print("\n" + "="*60)
        print("🎯 着色器代码行分析测试")
        print("="*60)
        print("📋 输入代码:")
        print("-" * 40)
        
        # 显示代码，添加行号
        lines = shader_content.strip().split('\n')
        for i, line in enumerate(lines, 1):
            print(f"{i:3d}: {line}")
        
        print("-" * 40)
        print(f"总行数: {len(lines)}")
        
        # 执行分析
        print("\n🔍 开始分析...")
        
        # 生成输出文件路径（在项目根目录）
        base_filename = str(self.project_root / output_name)
        
        try:
            result = self.processor.analyze_shader(
                shader_content, 
                save_reports=True, 
                base_filename=base_filename
            )
            
            if 'error' in result:
                print(f"❌ 分析失败: {result['error']}")
                return None
            
            print("✅ 分析完成!")
            
            # 显示生成的文件
            files = result.get('files', {})
            print(f"\n📄 生成的文件:")
            for file_type, file_path in files.items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"  {file_type.upper()}: {file_path} ({file_size} 字节)")
                else:
                    print(f"  {file_type.upper()}: {file_path} (文件不存在)")
            
            # 显示分析结果摘要
            self._display_analysis_summary(result)
            
            # 自动打开HTML文件
            if auto_open and 'html' in files:
                html_file = files['html']
                if os.path.exists(html_file):
                    print(f"\n🌐 正在打开HTML报告: {html_file}")
                    webbrowser.open(f'file:///{os.path.abspath(html_file).replace(os.sep, "/")}')
                else:
                    print(f"❌ HTML文件不存在: {html_file}")
            
            return result
            
        except Exception as e:
            print(f"❌ 分析过程中出现异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _display_analysis_summary(self, result):
        """显示分析结果摘要"""
        analysis = result.get('analysis', {})
        overall_stats = analysis.get('overall_statistics', {})
        
        print(f"\n📊 分析结果摘要:")
        print(f"  代码行数: {overall_stats.get('total_lines', 0)}")
        print(f"  变量声明: {overall_stats.get('total_variables', 0)}")
        print(f"  运算过程: {overall_stats.get('total_operations', 0)}")
        print(f"  临时变量: {overall_stats.get('total_temp_variables', 0)}")
        print(f"  类型转换: {overall_stats.get('total_type_conversions', 0)}")
        print(f"  精度问题: {overall_stats.get('total_precision_issues', 0)}")
        
        # 显示类型分布
        type_dist = overall_stats.get('type_distribution', {})
        if type_dist:
            print(f"\n🎨 类型分布:")
            sorted_types = sorted(type_dist.items(), key=lambda x: x[1], reverse=True)
            total_types = sum(type_dist.values())
            for type_name, count in sorted_types:
                percentage = (count / total_types) * 100 if total_types > 0 else 0
                print(f"  {type_name}: {count} ({percentage:.1f}%)")
        
        # 显示行分析概况
        line_analyses = analysis.get('line_analyses', [])
        analyzed_lines = len([la for la in line_analyses if hasattr(la, 'operation_process') and la.operation_process])
        print(f"\n📝 行分析概况:")
        print(f"  总行数: {len(line_analyses)}")
        print(f"  有分析的行: {analyzed_lines}")
        print(f"  分析覆盖率: {(analyzed_lines/len(line_analyses)*100):.1f}%" if line_analyses else "0%")


def main():
    """主函数 - 可以在这里修改测试用例"""
    
    # 创建测试实例
    tester = ShaderAnalysisTest()
    
    # 测试用例1: 基本的着色器代码
    test_shader_code = """
    float3 position = float3(1.0f, 2.0f, 3.0f);
    float2 uv = position.xy;
    float distance = length(uv);
    half alpha = saturate(distance / 10.0f);
    float4 color = float4(uv, alpha, 1.0f);
    """
    
    # 输入文件路径（如果为空则使用上面的字符串）
    input_file_path = "D:\XuSong\Project\shaderProfile\shaderProcess\metal_shader_ps"  # 例如: "test_shader.hlsl"
    
    # 输出文件名前缀
    output_name = "shader_line_analysis_test"
    
    print("🚀 着色器代码行分析测试工具")
    print("=" * 60)
    
    # 执行测试
    result = tester.test_shader_analysis(
        shader_code=test_shader_code,
        input_file=input_file_path,
        output_name=output_name,
        auto_open=True
    )
    
    if result:
        print("\n✅ 测试完成! HTML报告已生成并打开。")
        print("💡 提示: 你可以修改上面的test_shader_code变量来测试不同的着色器代码。")
    else:
        print("\n❌ 测试失败!")


if __name__ == "__main__":
    main()
