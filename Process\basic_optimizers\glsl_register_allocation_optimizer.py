"""
GLSL寄存器分配优化器 - 优化变量的寄存器使用，减少寄存器压力
"""
import re
from typing import Dict, List, Tuple, Set, Optional
from .glsl_optimizer_base import GLSLOptimizerBase, OptimizationLevel

class GLSLRegisterAllocationOptimizer(GLSLOptimizerBase):
    """
    GLSL寄存器分配优化器，优化变量的生命周期和寄存器使用。
    
    支持的优化：
    - 变量生命周期分析
    - 寄存器重用优化
    - 临时变量消除
    - 变量合并
    - 寄存器压力分析
    - 内存到寄存器的提升
    
    用法：
        optimizer = GLSLRegisterAllocationOptimizer()
        optimized_code = optimizer.optimize_code(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 寄存器压力阈值
        self.register_pressure_thresholds = {
            OptimizationLevel.BASIC: 16,
            OptimizationLevel.AGGRESSIVE: 24,
            OptimizationLevel.MAXIMUM: 32
        }
        
        # 变量类型的寄存器成本
        self.register_costs = {
            'float': 1,
            'vec2': 2,
            'vec3': 3,
            'vec4': 4,
            'int': 1,
            'ivec2': 2,
            'ivec3': 3,
            'ivec4': 4,
            'mat2': 4,
            'mat3': 9,
            'mat4': 16,
            'bool': 1,
            'bvec2': 2,
            'bvec3': 3,
            'bvec4': 4
        }
    
    def optimize_code(self, code: str) -> str:
        """优化GLSL寄存器分配代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._eliminate_dead_variables(optimized_code)
            optimized_code = self._merge_temporary_variables(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._optimize_variable_lifetime(optimized_code)
            optimized_code = self._reuse_registers(optimized_code)
            optimized_code = self._promote_to_registers(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._analyze_register_pressure(optimized_code)
            optimized_code = self._optimize_spill_code(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _eliminate_dead_variables(self, code: str) -> str:
        """消除死变量"""
        lines = code.splitlines()
        variable_usage = self._analyze_variable_usage(lines)
        optimized_lines = []

        for line in lines:
            # 优化：检测并移除未使用的变量声明
            var_decl = re.match(r'\s*(\w+)\s+(\w+)(?:\s*=\s*[^;]+)?\s*;', line.strip())
            if var_decl:
                var_type = var_decl.group(1)
                var_name = var_decl.group(2)

                # 优化：基于使用情况决定是否保留变量
                if var_name in variable_usage and variable_usage[var_name]['used']:
                    optimized_lines.append(line)
                else:
                    self.statistics['optimizations_applied'] += 1
                    continue
            else:
                optimized_lines.append(line)

        return '\n'.join(optimized_lines)
    
    def _merge_temporary_variables(self, code: str) -> str:
        """合并临时变量"""
        lines = code.splitlines()
        temp_vars = {}
        optimized_lines = []
        
        for line in lines:
            # 查找临时变量模式
            temp_match = re.match(r'\s*(\w+)\s+(\w+)\s*=\s*([^;]+);\s*$', line.strip())
            if temp_match:
                var_type = temp_match.group(1)
                var_name = temp_match.group(2)
                expression = temp_match.group(3)
                
                # 检查是否为简单的临时变量
                if self._is_simple_temporary(var_name, expression, lines):
                    # 尝试内联
                    inlined_code = self._inline_temporary_variable(var_name, expression, lines)
                    if inlined_code:
                        optimized_lines = inlined_code
                        self.statistics['optimizations_applied'] += 1
                        continue
            
            optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _optimize_variable_lifetime(self, code: str) -> str:
        """优化变量生命周期"""
        lines = code.splitlines()
        variable_lifetimes = self._analyze_variable_lifetimes(lines)
        optimized_lines = []
        
        # 重新排列变量声明，尽可能延迟声明
        for i, line in enumerate(lines):
            var_decl = re.match(r'\s*(\w+)\s+(\w+)(?:\s*=\s*[^;]+)?\s*;', line.strip())
            if var_decl:
                var_name = var_decl.group(2)
                
                # 检查是否可以延迟声明
                if var_name in variable_lifetimes:
                    first_use = variable_lifetimes[var_name]['first_use']
                    if first_use > i:
                        # 可以延迟声明
                        continue
            
            optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _reuse_registers(self, code: str) -> str:
        """重用寄存器"""
        lines = code.splitlines()
        variable_lifetimes = self._analyze_variable_lifetimes(lines)
        register_map = {}
        next_register = 0
        
        # 为变量分配寄存器
        for var_name, lifetime in variable_lifetimes.items():
            # 查找可重用的寄存器
            reusable_reg = None
            for reg_id, (assigned_var, end_time) in register_map.items():
                if end_time < lifetime['first_use']:
                    reusable_reg = reg_id
                    break
            
            if reusable_reg is not None:
                register_map[reusable_reg] = (var_name, lifetime['last_use'])
            else:
                register_map[next_register] = (var_name, lifetime['last_use'])
                next_register += 1
        
        # 生成寄存器重用建议
        if len(register_map) < len(variable_lifetimes):
            suggestion = f"// 寄存器优化：{len(variable_lifetimes)}个变量可以重用{len(register_map)}个寄存器\n"
            code = suggestion + code
            self.statistics['optimizations_applied'] += 1
        
        return code
    
    def _promote_to_registers(self, code: str) -> str:
        """提升变量到寄存器"""
        # 识别频繁访问的变量，建议提升到寄存器
        variable_access_counts = {}
        
        # 统计变量访问频率
        for var_name in re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', code):
            if not self.is_builtin_variable(var_name):
                variable_access_counts[var_name] = variable_access_counts.get(var_name, 0) + 1
        
        # 识别高频访问变量
        frequent_vars = [var for var, count in variable_access_counts.items() if count >= 5]
        
        if frequent_vars:
            # 移除在文件开头添加注释，避免破坏#version指令
            # suggestion = f"// 建议：考虑将高频访问变量提升到寄存器: {', '.join(frequent_vars[:3])}\n"
            # code = suggestion + code
            self.statistics['optimizations_applied'] += 1
        
        return code
    
    def _analyze_register_pressure(self, code: str) -> str:
        """分析寄存器压力"""
        lines = code.splitlines()
        max_pressure = 0
        current_pressure = 0
        
        active_variables = set()
        variable_lifetimes = self._analyze_variable_lifetimes(lines)
        
        for i, line in enumerate(lines):
            # 更新活跃变量集合
            for var_name, lifetime in variable_lifetimes.items():
                if lifetime['first_use'] == i:
                    active_variables.add(var_name)
                elif lifetime['last_use'] == i:
                    active_variables.discard(var_name)
            
            # 计算当前寄存器压力
            current_pressure = sum(self._get_variable_register_cost(var, code) for var in active_variables)
            max_pressure = max(max_pressure, current_pressure)
        
        threshold = self.register_pressure_thresholds[self.optimization_level]
        if max_pressure > threshold:
            warning = f"// 警告：寄存器压力过高 ({max_pressure} > {threshold})，考虑优化变量使用\n"
            code = warning + code
            self.statistics['optimizations_applied'] += 1
        
        return code
    
    def _optimize_spill_code(self, code: str) -> str:
        """优化溢出代码"""
        # 当寄存器压力过高时，优化变量溢出到内存的策略
        return code
    
    def _analyze_variable_usage(self, lines: List[str]) -> Dict[str, Dict]:
        """分析变量使用情况"""
        variable_usage = {}
        
        for i, line in enumerate(lines):
            # 查找变量声明
            var_decl = re.match(r'\s*(\w+)\s+(\w+)(?:\s*=\s*[^;]+)?\s*;', line.strip())
            if var_decl:
                var_name = var_decl.group(2)
                variable_usage[var_name] = {
                    'declared': i,
                    'used': False,
                    'type': var_decl.group(1)
                }
            
            # 查找变量使用
            for var_name in variable_usage:
                if var_name in line and i != variable_usage[var_name]['declared']:
                    variable_usage[var_name]['used'] = True
        
        return variable_usage
    
    def _analyze_variable_lifetimes(self, lines: List[str]) -> Dict[str, Dict]:
        """分析变量生命周期"""
        variable_lifetimes = {}
        
        for i, line in enumerate(lines):
            # 查找变量声明
            var_decl = re.match(r'\s*(\w+)\s+(\w+)(?:\s*=\s*[^;]+)?\s*;', line.strip())
            if var_decl:
                var_name = var_decl.group(2)
                variable_lifetimes[var_name] = {
                    'declared': i,
                    'first_use': i,
                    'last_use': i,
                    'type': var_decl.group(1)
                }
            
            # 查找变量使用
            for var_name in variable_lifetimes:
                if var_name in line:
                    if variable_lifetimes[var_name]['first_use'] == variable_lifetimes[var_name]['declared']:
                        variable_lifetimes[var_name]['first_use'] = i
                    variable_lifetimes[var_name]['last_use'] = i
        
        return variable_lifetimes
    
    def _is_simple_temporary(self, var_name: str, expression: str, lines: List[str]) -> bool:
        """检查是否为简单的临时变量"""
        # 检查变量是否只使用一次
        usage_count = 0
        for line in lines:
            usage_count += line.count(var_name)
        
        # 如果只使用一次（除了声明），且表达式简单，可以内联
        return usage_count == 2 and len(expression.split()) <= 3
    
    def _inline_temporary_variable(self, var_name: str, expression: str, lines: List[str]) -> Optional[List[str]]:
        """内联临时变量"""
        inlined_lines = []
        var_inlined = False
        
        for line in lines:
            if f'{var_name} =' in line and not var_inlined:
                # 跳过变量声明
                continue
            elif var_name in line and not var_inlined:
                # 替换变量使用
                new_line = line.replace(var_name, f'({expression})')
                inlined_lines.append(new_line)
                var_inlined = True
            else:
                inlined_lines.append(line)
        
        return inlined_lines if var_inlined else None
    
    def _get_variable_register_cost(self, var_name: str, code: str) -> int:
        """获取变量的寄存器成本"""
        # 查找变量类型
        var_decl = re.search(rf'\b(\w+)\s+{re.escape(var_name)}\b', code)
        if var_decl:
            var_type = var_decl.group(1)
            return self.register_costs.get(var_type, 1)
        
        return 1  # 默认成本
