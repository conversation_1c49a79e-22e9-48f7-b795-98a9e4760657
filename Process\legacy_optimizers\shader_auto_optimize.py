import re
from collections import defaultdict

class ShaderAutoOptimizer:
    """
    HLSL自动优化器，支持多种GPU优化规则。
    用法：
        optimizer = ShaderAutoOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)

    支持的主要优化：
    - float/half类型智能替换（根据变量名）
    - int->uint（位运算/索引场景）
    - int->min16int（for循环计数器）
    - clamp(x,0,1)→saturate(x)
    - a*b+c→mad(a,b,c)
    - 小函数自动inline
    - 固定次数循环自动加[unroll]
    - if-else分支无分支化
    - static const常量折叠
    - 冗余类型转换消除
    - 条件表达式简化
    - pow(x,2.0)→x*x
    - saturate(abs(x))→abs(x)
    - lerp边界合并
    - float3/float4构造合并
    - 死代码删除（if(false)、return后）
    - swizzle简化
    - 除法转乘法倒数
    - 标量合并为向量
    - 未使用变量/函数删除、常量传播
    """
    def optimize_code(self, code: str) -> str:
        # 逐行处理，便于正则和结构优化
        lines = code.splitlines(keepends=True)
        new_lines = []
        in_func = False
        func_lines = []
        func_header = ''
        used_vars = set()
        declared_vars = set()
        func_names = set()
        used_funcs = set()
        const_assigns = {}
        var_assigns = defaultdict(list)
        # 类型优化关键字
        highp_keys = ['world', 'position', 'depth', 'z', 'pos', 'coord']
        lowp_keys = ['color', 'colour', 'uv', 'texcoord', 'normal', 'tangent']
        # 类型声明正则
        type_var_pattern = re.compile(r'(float|half)(\d*[x\d]*)?\s+([a-zA-Z_][a-zA-Z0-9_]*)')
        def type_optimize_replacer(m):
            t, suffix, name = m.group(1), m.group(2) or '', m.group(3).lower()
            # half->float（高精度变量）
            if t == 'half' and any(key in name for key in highp_keys):
                return 'float' + suffix + ' ' + m.group(3)
            # float->half（低精度变量）
            if t == 'float' and any(key in name for key in lowp_keys):
                return 'half' + suffix + ' ' + m.group(3)
            return m.group(0)
        # int->uint（位运算/索引）
        int2uint_pattern = re.compile(r'(int)(\d*)\s+([a-zA-Z_][a-zA-Z0-9_]*)')
        # int->min16int（for循环）
        for_pattern = re.compile(r'(for\s*\(\s*)(int)(\s+[a-zA-Z_][a-zA-Z0-9_]*\s*=)')
        # clamp(x,0,1)→saturate(x)
        clamp_pattern = re.compile(r'clamp\(([^,]+),\s*0\s*,\s*1\s*\)')
        # a*b+c→mad(a,b,c)
        mad_pattern = re.compile(r'([a-zA-Z0-9_\.]+)\s*\*\s*([a-zA-Z0-9_\.]+)\s*\+\s*([a-zA-Z0-9_\.]+)')
        # 小函数自动inline
        func_def_pattern = re.compile(r'^\s*(?:inline|static|export)?\s*([a-zA-Z0-9_]+)\s+([a-zA-Z0-9_]+)\s*\([^)]*\)')
        # 固定次数循环自动加[unroll]
        fixed_for_pattern = re.compile(r'(for\s*\(\s*(?:int|min16int|uint)\s+[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*\d+;\s*[a-zA-Z_][a-zA-Z0-9_]*\s*[<>=!]+\s*\d+;.*\))')
        # if-else分支无分支化
        if_else_pattern = re.compile(r'if\s*\(([^)]+)\)\s*([a-zA-Z0-9_\.]+)\s*=\s*([^;]+);\s*else\s*\2\s*=\s*([^;]+);')
        # static const常量
        const_assign_pattern = re.compile(r'^\s*(float|int|uint|half|min16float|min16int)\s+([A-Z0-9_]+)\s*=\s*([^;]+);', re.I)
        # 冗余类型转换消除
        redundant_cast_pattern = re.compile(r'(float|int|uint|half|min16float|min16int)\s*\(\s*\1\s*\(([^)]+)\)\s*\)')
        # 条件表达式简化
        cond_expr_pattern = re.compile(r'([a-zA-Z0-9_\.]+)\s*\?\s*true\s*:\s*false')
        # pow(x,2.0)→x*x
        pow2_pattern = re.compile(r'pow\(([^,]+),\s*2\.0\s*\)')
        # saturate(abs(x))→abs(x)
        saturate_abs_pattern = re.compile(r'saturate\(abs\(([^)]+)\)\)')
        # lerp边界合并
        lerp0_pattern = re.compile(r'lerp\(([^,]+),\s*([^,]+),\s*0\s*\)')
        lerp1_pattern = re.compile(r'lerp\(([^,]+),\s*([^,]+),\s*1\s*\)')
        # float3/float4构造合并
        float3_same_pattern = re.compile(r'float3\(([^,]+),\s*\1,\s*\1\)')
        float4_xyz1_pattern = re.compile(r'float4\(([^,]+)\.xyz,\s*1\.0\)')
        # 死代码删除（if(false)、return后）
        if_false_pattern = re.compile(r'if\s*\(\s*false\s*\)\s*{[^}]*}')
        # 合并相邻纹理采样（简单启发式）
        tex_sample_pattern = re.compile(r'([a-zA-Z0-9_]+)\s*=\s*([a-zA-Z0-9_]+)\.Sample\(([^)]+)\);')
        # swizzle简化
        swizzle_pattern = re.compile(r'([a-zA-Z0-9_]+)\.([rgba]{2,4})\.\2')
        # 除法转乘法倒数
        div_pattern = re.compile(r'([a-zA-Z0-9_\.]+)\s*/\s*([a-zA-Z0-9_\.]+)')
        # 矩阵/向量乘法合并（启发式）
        matmul_pattern = re.compile(r'(mul\([^;]+\))\s*([+\-*/])\s*(mul\([^;]+\))')
        # 标量合并为向量
        rgb_assign_pattern = re.compile(r'([a-zA-Z0-9_]+)\.r\s*=\s*([^;]+);\s*\1\.g\s*=\s*([^;]+);\s*\1\.b\s*=\s*([^;]+);')
        # 着色器入口函数白名单
        shader_entry_points = {n.lower() for n in ['main', 'VSMain', 'PSMain', 'CSMain', 'FSMain', 'GSMain', 'HSMain', 'DSMain']}
        # 预处理：收集变量/函数声明和使用
        for line in lines:
            var_decl = re.match(r'\s*(float|int|uint|half|min16float|min16int)\s+([a-zA-Z_][a-zA-Z0-9_]*)', line)
            if var_decl:
                declared_vars.add(var_decl.group(2))
            for v in declared_vars:
                if re.search(rf'\b{v}\b', line):
                    used_vars.add(v)
            func_decl = func_def_pattern.match(line)
            if func_decl:
                func_names.add(func_decl.group(2))
            for f in func_names:
                if re.search(rf'\b{f}\s*\(', line) and not func_def_pattern.match(line):
                    used_funcs.add(f)
            const_m = const_assign_pattern.match(line)
            if const_m:
                const_assigns[const_m.group(2)] = const_m.group(3)
            var_assign = re.match(r'\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*([^;]+);', line)
            if var_assign:
                var_assigns[var_assign.group(1)].append(var_assign.group(2))
        i = 0
        while i < len(lines):
            line = lines[i]
            # 类型优化：float<->half
            line = type_var_pattern.sub(type_optimize_replacer, line)
            # int->uint（位运算/索引）
            if any(op in line for op in ['&', '|', '^', '~']):
                line = int2uint_pattern.sub(lambda m: m.group(0).replace('int', 'uint', 1), line)
            # int->min16int（for循环）
            line = for_pattern.sub(lambda m: m.group(1) + 'min16int' + m.group(3), line)
            # clamp(x,0,1)→saturate(x)
            line = clamp_pattern.sub(r'saturate(\1)', line)
            # a*b+c→mad(a,b,c)
            if not any(kw in line for kw in ['for', 'if', 'while', 'return', 'static', 'const']):
                line = mad_pattern.sub(r'mad(\1, \2, \3)', line)
            # static const常量
            line = const_assign_pattern.sub(r'static const \1 \2 = \3;', line)
            # 固定次数循环自动加[unroll]
            if fixed_for_pattern.search(line):
                line = '[unroll]\n' + line
            # if-else分支无分支化
            if i+1 < len(lines):
                if_match = if_else_pattern.search(''.join(lines[i:i+2]))
                if if_match:
                    cond, var, a, b = if_match.groups()
                    new_line = f'{var} = lerp({b.strip()}, {a.strip()}, saturate(sign({cond.strip()}) * 0.5 + 0.5));\n'
                    new_lines.append(new_line)
                    i += 2
                    continue
            # 小函数自动inline
            if func_def_pattern.match(line):
                func_header = line
                func_lines = [line]
                in_func = True
                i += 1
                while in_func and i < len(lines):
                    func_lines.append(lines[i])
                    if '}' in lines[i]:
                        if len(func_lines) <= 5 and not func_lines[0].lstrip().startswith('inline'):
                            func_lines[0] = func_lines[0].replace(func_lines[0].lstrip(), 'inline ' + func_lines[0].lstrip(), 1)
                        new_lines.extend(func_lines)
                        in_func = False
                    i += 1
                continue
            # 冗余类型转换消除
            line = redundant_cast_pattern.sub(r'\1(\2)', line)
            # 条件表达式简化
            line = cond_expr_pattern.sub(r'\1', line)
            # pow(x,2.0)→x*x
            line = pow2_pattern.sub(r'(\1)*(\1)', line)
            # saturate(abs(x))→abs(x)
            line = saturate_abs_pattern.sub(r'abs(\1)', line)
            # lerp边界合并
            line = lerp0_pattern.sub(r'\1', line)
            line = lerp1_pattern.sub(r'\2', line)
            # float3/float4构造合并
            line = float3_same_pattern.sub(r'float3(\1)', line)
            line = float4_xyz1_pattern.sub(r'float4(\1, 1.0)', line)
            # 死代码删除（if(false)）
            line = if_false_pattern.sub('', line)
            # swizzle简化
            line = swizzle_pattern.sub(r'\1.\2', line)
            # 除法转乘法倒数
            line = div_pattern.sub(r'\1 * rcp(\2)', line)
            # 标量合并为向量
            line = rgb_assign_pattern.sub(r'\1.rgb = float3(\2, \3, \4);', line)
            new_lines.append(line)
            i += 1
        out_code = ''.join(new_lines)
        # 未使用变量/函数检测与删除（启发式，仅删除未被用到的局部变量/函数声明）
        for v in declared_vars:
            if v not in used_vars:
                out_code = re.sub(rf'\s*(float|int|uint|half|min16float|min16int)\s+{v}[^;]*;\n?', '', out_code)
        for f in func_names:
            if f.lower() not in used_funcs and f.lower() not in shader_entry_points:
                out_code = re.sub(rf'\s*[a-zA-Z0-9_]+\s+{f}\s*\([^)]*\)\s*{{[^}}]*}}', '', out_code, flags=re.DOTALL)
        # 常量传播（单次赋值直接替换）
        for v, assigns in var_assigns.items():
            if len(assigns) == 1:
                out_code = re.sub(rf'\b{v}\b', assigns[0], out_code)
        return out_code 