"""
HLSL寄存器分配优化器 - 优化变量的寄存器使用，减少寄存器压力
"""
import re
from typing import Dict, List, Tuple, Set, Optional
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel

class HLSLRegisterAllocationOptimizer(HLSLOptimizerBase):
    """
    寄存器分配优化器，优化变量的生命周期和寄存器使用。
    
    支持的优化：
    - 变量生命周期分析
    - 寄存器重用优化
    - 临时变量消除
    - 变量合并
    - 寄存器压力分析
    - 内存到寄存器的提升
    
    用法：
        optimizer = RegisterAllocationOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 寄存器使用统计
        self.register_usage = {
            'scalar_registers': 0,
            'vector_registers': 0,
            'temp_registers': 0,
            'max_concurrent': 0
        }
        
        # 变量生命周期信息
        self.variable_lifetimes = {}
        
        # 寄存器分配映射
        self.register_allocation = {}
    
    def optimize_code(self, code: str) -> str:
        """优化寄存器分配"""
        self.reset_statistics()
        
        # 分析变量生命周期
        self._analyze_variable_lifetimes(code)
        
        # 分析寄存器使用
        self._analyze_register_usage(code)
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            # 暂时禁用临时变量消除，因为它可能创建错误的引用
            # optimized_code = self._eliminate_temporary_variables(optimized_code)
            # 暂时禁用变量重用，因为它可能创建错误的映射
            # optimized_code = self._reuse_dead_variables(optimized_code)
            pass
        
        # 暂时禁用变量合并，因为它可能导致问题
        # if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
        #     optimized_code = self._merge_compatible_variables(optimized_code)
        #     optimized_code = self._optimize_variable_scope(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._perform_register_coalescing(optimized_code)
            optimized_code = self._add_register_hints(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code

    def _is_important_variable_declaration(self, var_name, var_expr):
        """判断是否是重要的变量声明，不应该被内联"""
        # 如果变量表达式包含函数调用，通常是重要的计算
        function_calls = ['normalize', 'dot', 'cross', 'reflect', 'length', 'distance',
                         'sin', 'cos', 'tan', 'pow', 'sqrt', 'abs', 'min', 'max']

        for func in function_calls:
            if func + '(' in var_expr:
                return True

        # 如果变量名暗示它是重要的语义变量
        important_names = ['viewDir', 'lightDir', 'normal', 'worldPos', 'worldNormal',
                          'halfDir', 'reflectDir', 'texcoord', 'uv']

        if var_name in important_names:
            return True

        return False
    
    def _analyze_variable_lifetimes(self, code: str):
        """分析变量生命周期"""
        lines = code.splitlines()
        
        # 第一遍：找到所有变量声明
        for line_num, line in enumerate(lines):
            var_decl = re.search(r'\b(float|half|int|uint|bool|min16float|min16int)(\d*(?:x\d+)?)\s+([a-zA-Z_][a-zA-Z0-9_]*)', line)
            
            if var_decl:
                var_type = var_decl.group(1) + (var_decl.group(2) or '')
                var_name = var_decl.group(3)
                
                self.variable_lifetimes[var_name] = {
                    'type': var_type,
                    'declared_line': line_num,
                    'first_use': None,
                    'last_use': None,
                    'usage_count': 0,
                    'is_temporary': False,
                    'scope_depth': self._calculate_scope_depth(lines, line_num)
                }
        
        # 第二遍：分析变量使用
        for line_num, line in enumerate(lines):
            for var_name in self.variable_lifetimes:
                if re.search(rf'\b{var_name}\b', line) and line_num != self.variable_lifetimes[var_name]['declared_line']:
                    lifetime = self.variable_lifetimes[var_name]
                    
                    if lifetime['first_use'] is None:
                        lifetime['first_use'] = line_num
                    
                    lifetime['last_use'] = line_num
                    lifetime['usage_count'] += 1
        
        # 标记临时变量
        self._identify_temporary_variables()
    
    def _analyze_register_usage(self, code: str):
        """分析寄存器使用情况"""
        concurrent_vars = {}
        
        lines = code.splitlines()
        
        for line_num in range(len(lines)):
            active_vars = []
            
            # 找到在当前行活跃的变量
            for var_name, lifetime in self.variable_lifetimes.items():
                if (lifetime['declared_line'] <= line_num and 
                    (lifetime['last_use'] is None or lifetime['last_use'] >= line_num)):
                    active_vars.append(var_name)
            
            concurrent_vars[line_num] = active_vars
            
            # 更新最大并发数
            if len(active_vars) > self.register_usage['max_concurrent']:
                self.register_usage['max_concurrent'] = len(active_vars)
        
        # 统计不同类型的寄存器使用
        for var_name, lifetime in self.variable_lifetimes.items():
            var_type = lifetime['type']
            
            if var_type in ['float', 'half', 'int', 'uint', 'bool']:
                self.register_usage['scalar_registers'] += 1
            elif any(vec_type in var_type for vec_type in ['2', '3', '4']):
                self.register_usage['vector_registers'] += 1
            
            if lifetime['is_temporary']:
                self.register_usage['temp_registers'] += 1
    
    def _eliminate_temporary_variables(self, code: str) -> str:
        """消除临时变量"""
        lines = code.splitlines()
        optimized_lines = []
        eliminated_vars = set()
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # 查找临时变量声明和使用
            temp_var_pattern = re.search(r'(\w+)\s+(\w+)\s*=\s*([^;]+);', line)
            
            if temp_var_pattern and i + 1 < len(lines):
                var_type = temp_var_pattern.group(1)
                var_name = temp_var_pattern.group(2)
                var_expr = temp_var_pattern.group(3)
                
                # 检查下一行是否立即使用这个变量
                next_line = lines[i + 1]
                
                # 更严格的临时变量内联条件
                if (var_name in self.variable_lifetimes and
                    self.variable_lifetimes[var_name]['usage_count'] == 1 and
                    var_name in next_line and
                    # 确保不是重要的变量声明（避免删除在函数内重新声明的变量）
                    not self._is_important_variable_declaration(var_name, var_expr)):
                    
                    # 可以内联这个临时变量（使用单词边界匹配）
                    inlined_line = re.sub(rf'\b{re.escape(var_name)}\b', f'({var_expr})', next_line)

                    # 移除调试注释，直接添加优化后的代码
                    optimized_lines.append(inlined_line)
                    
                    eliminated_vars.add(var_name)
                    self.statistics['optimizations_applied'] += 1
                    i += 2  # 跳过下一行
                    continue
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _reuse_dead_variables(self, code: str) -> str:
        """重用死变量的寄存器"""
        lines = code.splitlines()
        optimized_lines = []
        
        # 构建变量重用映射
        reuse_mapping = self._build_reuse_mapping()
        
        for line in lines:
            optimized_line = line
            
            # 应用重用映射
            for old_var, new_var in reuse_mapping.items():
                # 使用单词边界匹配，避免部分匹配
                if re.search(rf'\b{re.escape(old_var)}\b', line):
                    # 检查是否在声明行
                    if re.search(rf'\b(float|half|int|uint)\d*\s+{re.escape(old_var)}\b', line):
                        optimized_lines.append(f'    // Reusing register from {new_var}')
                        self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(optimized_line)
        
        return '\n'.join(optimized_lines)
    
    def _merge_compatible_variables(self, code: str) -> str:
        """合并兼容的变量"""
        lines = code.splitlines()
        optimized_lines = []
        
        # 找到可以合并的变量对
        mergeable_pairs = self._find_mergeable_variables()
        
        for line in lines:
            optimized_line = line
            
            # 应用变量合并
            skip_line = False
            for var1, var2 in mergeable_pairs:
                # 使用单词边界匹配，避免部分匹配
                if re.search(rf'\b{re.escape(var2)}\b', line) and var1 != var2:
                    # 如果是var2的声明行，标记跳过
                    if re.search(rf'\b(float|half|int|uint)\d*\s+{re.escape(var2)}\b', line):
                        self.statistics['optimizations_applied'] += 1
                        skip_line = True
                        break
                    else:
                        # 将var2替换为var1
                        optimized_line = re.sub(rf'\b{re.escape(var2)}\b', var1, optimized_line)

            if skip_line:
                continue
            
            optimized_lines.append(optimized_line)
        
        return '\n'.join(optimized_lines)
    
    def _optimize_variable_scope(self, code: str) -> str:
        """优化变量作用域"""
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            # 查找变量声明
            var_decl = re.search(r'\b(float|half|int|uint|bool)(\d*(?:x\d+)?)\s+([a-zA-Z_][a-zA-Z0-9_]*)', line)
            
            if var_decl:
                var_name = var_decl.group(3)
                
                if var_name in self.variable_lifetimes:
                    lifetime = self.variable_lifetimes[var_name]
                    
                    # 如果变量的作用域很小，建议移动到更小的作用域
                    if (lifetime['last_use'] and 
                        lifetime['last_use'] - lifetime['declared_line'] < 5):
                        
                        # 移除作用域建议注释
                        self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _perform_register_coalescing(self, code: str) -> str:
        """执行寄存器合并"""
        lines = code.splitlines()
        optimized_lines = []
        
        # 查找可以合并的寄存器分配
        for line in lines:
            # 查找简单的赋值操作 a = b;
            simple_assignment = re.search(r'(\w+)\s*=\s*(\w+)\s*;', line)
            
            if simple_assignment:
                target_var = simple_assignment.group(1)
                source_var = simple_assignment.group(2)
                
                # 检查是否可以合并
                if (target_var in self.variable_lifetimes and 
                    source_var in self.variable_lifetimes):
                    
                    target_lifetime = self.variable_lifetimes[target_var]
                    source_lifetime = self.variable_lifetimes[source_var]
                    
                    # 如果源变量在此后不再使用，可以合并
                    if (source_lifetime['last_use'] and 
                        source_lifetime['last_use'] <= target_lifetime['declared_line']):
                        
                        optimized_lines.append(f'    // Register coalescing opportunity: {target_var} = {source_var}')
                        self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _add_register_hints(self, code: str) -> str:
        """添加寄存器使用提示"""
        lines = code.splitlines()
        optimized_lines = []
        
        # 在文件开头添加寄存器使用摘要
        optimized_lines.append("// Register Usage Analysis:")
        optimized_lines.append(f"// Max concurrent variables: {self.register_usage['max_concurrent']}")
        optimized_lines.append(f"// Scalar registers: {self.register_usage['scalar_registers']}")
        optimized_lines.append(f"// Vector registers: {self.register_usage['vector_registers']}")
        optimized_lines.append(f"// Temporary registers: {self.register_usage['temp_registers']}")
        optimized_lines.append("")
        
        # 添加高寄存器压力警告
        if self.register_usage['max_concurrent'] > 32:  # 假设的阈值
            optimized_lines.append("// WARNING: High register pressure detected!")
            optimized_lines.append("// Consider reducing variable count or optimizing lifetimes")
            optimized_lines.append("")
        
        optimized_lines.extend(lines)
        
        return '\n'.join(optimized_lines)
    
    def _calculate_scope_depth(self, lines: List[str], line_num: int) -> int:
        """计算作用域深度"""
        depth = 0
        
        for i in range(line_num + 1):
            line = lines[i]
            depth += line.count('{') - line.count('}')
        
        return max(0, depth)
    
    def _identify_temporary_variables(self):
        """识别临时变量"""
        for var_name, lifetime in self.variable_lifetimes.items():
            # 临时变量的特征：
            # 1. 生命周期很短
            # 2. 使用次数少
            # 3. 变量名包含temp等关键字
            
            if (lifetime['last_use'] and 
                lifetime['last_use'] - lifetime['declared_line'] <= 2):
                lifetime['is_temporary'] = True
            
            # 只有完全未使用的变量才标记为临时变量（死代码）
            if lifetime['usage_count'] == 0:
                lifetime['is_temporary'] = True
            
            if any(temp_hint in var_name.lower() for temp_hint in ['temp', 'tmp', '_t', 'intermediate']):
                lifetime['is_temporary'] = True
    
    def _build_reuse_mapping(self) -> Dict[str, str]:
        """构建变量重用映射"""
        reuse_mapping = {}
        
        # 按生命周期排序变量
        sorted_vars = sorted(self.variable_lifetimes.items(), 
                           key=lambda x: (x[1]['declared_line'], x[1]['last_use'] or 999))
        
        for i, (var1, lifetime1) in enumerate(sorted_vars):
            for j, (var2, lifetime2) in enumerate(sorted_vars[i+1:], i+1):
                # 检查生命周期是否不重叠
                if (lifetime1['last_use'] and 
                    lifetime2['declared_line'] > lifetime1['last_use'] and
                    lifetime1['type'] == lifetime2['type']):
                    
                    reuse_mapping[var2] = var1
                    break
        
        return reuse_mapping
    
    def _find_mergeable_variables(self) -> List[Tuple[str, str]]:
        """找到可以合并的变量"""
        mergeable_pairs = []
        
        var_list = list(self.variable_lifetimes.items())
        
        for i, (var1, lifetime1) in enumerate(var_list):
            for j, (var2, lifetime2) in enumerate(var_list[i+1:], i+1):
                # 检查是否可以合并
                if (lifetime1['type'] == lifetime2['type'] and
                    self._can_merge_variables(lifetime1, lifetime2)):
                    
                    mergeable_pairs.append((var1, var2))
        
        return mergeable_pairs
    
    def _can_merge_variables(self, lifetime1: Dict, lifetime2: Dict) -> bool:
        """检查两个变量是否可以合并"""
        # 简化的实现：检查生命周期是否不重叠
        if not lifetime1['last_use'] or not lifetime2['last_use']:
            return False

        # 不要合并生命周期重叠的变量
        if not (lifetime1['last_use'] < lifetime2['declared_line'] or
                lifetime2['last_use'] < lifetime1['declared_line']):
            return False

        # 不要合并结构体成员变量（通常在文件开头声明）
        # 结构体成员通常在前50行内声明
        if lifetime1['declared_line'] < 50 or lifetime2['declared_line'] < 50:
            return False

        # 不要合并重要的语义变量
        return True
    
    def get_register_statistics(self) -> Dict:
        """获取寄存器使用统计"""
        stats = self.register_usage.copy()
        stats.update({
            'total_variables': len(self.variable_lifetimes),
            'temporary_variables': sum(1 for lt in self.variable_lifetimes.values() if lt['is_temporary']),
            'optimization_potential': self.register_usage['temp_registers'] + 
                                    max(0, self.register_usage['max_concurrent'] - 16)
        })
        return stats
