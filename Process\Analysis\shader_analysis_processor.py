#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析处理器 - 基于AST的精确类型分析
"""

from typing import Dict, Optional, List
from .shader_type_analyzer import ShaderTypeAnalyzer
from .analysis_report_generator import AnalysisReportGenerator, CodeLineData

class ShaderAnalysisProcessor:
    """着色器分析处理器 - 基于AST的精确类型分析"""

    def __init__(self):
        self.type_analyzer = ShaderTypeAnalyzer()
        self.report_generator = AnalysisReportGenerator()

    def analyze_shader(self, shader_content: str, save_reports: bool = True, base_filename: str = "shader_analysis") -> Dict:
        """
        分析着色器内容 - 使用基于AST的精确类型分析

        Args:
            shader_content: 着色器源代码内容
            save_reports: 是否保存报告文件
            base_filename: 报告文件基础名称

        Returns:
            包含精确分析结果和报告路径的字典
        """
        # 执行基于AST的精确类型分析
        analysis_result = self.type_analyzer.analyze_shader_with_precise_types(shader_content)

        result = {
            'analysis': analysis_result,
            'files': {},
        }

        # 如果需要保存报告文件
        if save_reports:
            try:
                file_paths = self._save_reports(
                    result,
                    shader_content,
                    base_filename
                )
                result['files'] = file_paths
            except Exception as e:
                result['error'] = f"保存报告文件时出错: {str(e)}"

        # 保存着色器内容供后续使用
        result['shader_content'] = shader_content

        return result

    def _save_reports(self, result: Dict, shader_content: str, base_filename: str) -> Dict[str, str]:
        """保存分析报告文件"""
        file_paths = {}
        precise_result = result['analysis']

        try:
            # 保存JSON报告
            json_filename = f"{base_filename}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                import json
                # 使用自定义序列化函数
                serializable_result = self._make_serializable(result)
                json.dump(serializable_result, f, indent=2, ensure_ascii=False)
            file_paths['json'] = json_filename

            # 保存HTML报告
            html_filename = f"{base_filename}.html"
            html_content = self._generate_html_report(result, shader_content)
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            file_paths['html'] = html_filename

            # 保存文本摘要报告
            txt_filename = f"{base_filename}_summary.txt"
            summary_content = self._generate_summary_report(precise_result)
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            file_paths['summary'] = txt_filename

        except Exception as e:
            raise Exception(f"保存分析报告失败: {str(e)}")

        return file_paths


    def _generate_summary_report(self, precise_data: Dict) -> str:
        """生成精确分析摘要报告"""
        if 'overall_statistics' in precise_data:
            # 使用精确分析结果
            overall_stats = precise_data['overall_statistics']

            summary_lines = []
            summary_lines.append("🎯 语法树分析摘要报告")
            summary_lines.append("=" * 50)

            # 基本统计
            summary_lines.append(f"📊 基本统计:")
            summary_lines.append(f"  总代码行: {overall_stats['total_lines']}")
            summary_lines.append(f"  变量声明: {overall_stats['total_variables']}")
            summary_lines.append(f"  运算过程: {overall_stats['total_operations']}")
            summary_lines.append(f"  临时变量: {overall_stats['total_temp_variables']}")

            # 类型分析
            summary_lines.append(f"\n🔍 类型分析:")
            summary_lines.append(f"  类型转换: {overall_stats['total_type_conversions']}")
            summary_lines.append(f"  精度问题: {overall_stats['total_precision_issues']}")

            # 类型分布
            if overall_stats['type_distribution']:
                summary_lines.append(f"\n🎨 类型分布:")
                total_types = sum(overall_stats['type_distribution'].values())
                for type_name, count in sorted(overall_stats['type_distribution'].items()):
                    percentage = count / total_types * 100 if total_types > 0 else 0
                    summary_lines.append(f"  {type_name}: {count} ({percentage:.1f}%)")

            # 语法树分析特点
            summary_lines.append(f"\n📈 语法树分析特点:")
            summary_lines.append(f"  基于AST的精确语法解析")
            summary_lines.append(f"  完整的运算过程分解")
            summary_lines.append(f"  临时变量类型推断")
            summary_lines.append(f"  向量成员访问支持")

            # 性能建议
            summary_lines.append(f"\n💡 性能建议:")
            if overall_stats['total_type_conversions'] > 0:
                summary_lines.append(f"  ⚠️  发现 {overall_stats['total_type_conversions']} 个类型转换，建议优化")
            if overall_stats['total_precision_issues'] > 0:
                summary_lines.append(f"  ⚠️  发现 {overall_stats['total_precision_issues']} 个混合精度问题，建议统一精度")
            if overall_stats['total_type_conversions'] == 0 and overall_stats['total_precision_issues'] == 0:
                summary_lines.append(f"  ✅ 代码类型使用良好，无明显性能问题")

            return "\n".join(summary_lines)
        else:
            return "无摘要总结"


    def _generate_html_report(self, result: Dict, shader_content: str) -> str:
        """生成HTML报告"""
        precise_result = result['analysis']

        # 构建代码行数据结构
        code_lines_data = self._build_code_lines_data(result, shader_content)

        # 使用报告生成器生成HTML
        return self.report_generator.generate_precise_html_report(
            precise_result,
            code_lines_data
        )

    def _build_code_lines_data(self, result: Dict, shader_content: str) -> List[CodeLineData]:
        """构建代码行数据结构"""

        precise_data = result['analysis']
        shader_lines = shader_content.split('\n')

        # 创建分析结果的映射（按行号）
        analysis_map = {}
        if 'line_analyses' in precise_data:
            for line_analysis in precise_data['line_analyses']:
                analysis_map[line_analysis.line_number] = line_analysis

        # 构建所有代码行的数据结构
        code_lines_data = []
        for line_num, line_content in enumerate(shader_lines, 1):
            analysis = analysis_map.get(line_num)

            if analysis:
                # 有分析结果的行
                operation_count = len(analysis.operation_process)
                conversion_count = len(analysis.type_conversions)
                precision_issues = len(analysis.precision_issues)

                # 计算节点详情
                node_details = []

                # 从运算过程中提取信息
                for op in analysis.operation_process:
                    node_details.append(f"operation({op.string})")

                # 从语法树中提取节点信息
                if analysis.syntax_tree:
                    self._extract_node_details(analysis.syntax_tree, node_details)

                # 确定性能级别
                performance_level = "normal"
                if conversion_count > 0:
                    performance_level = "conversion"
                elif precision_issues > 0:
                    performance_level = "issue"
                elif operation_count > 5:
                    performance_level = "intensive"

                # CSS类
                css_classes = []
                if analysis.type_conversions:
                    css_classes.append("type-conversion")
                if analysis.precision_issues:
                    css_classes.append("precision-issue")

                code_line_data = CodeLineData(
                    line_number=line_num,
                    content=line_content,
                    operation_count=operation_count,
                    conversion_count=conversion_count,
                    precision_issues=precision_issues,
                    node_details=node_details,
                    has_analysis=True,
                    css_classes=css_classes,
                    performance_level=performance_level
                )
            else:
                # 没有分析结果的行
                code_line_data = CodeLineData(
                    line_number=line_num,
                    content=line_content,
                    has_analysis=False
                )

            code_lines_data.append(code_line_data)

        return code_lines_data
    
    def _extract_node_details(self, node, node_details: List[str]):
        """从语法树节点中提取详细信息"""
        if node is None:
            return

        # 根据节点类型添加详情
        if hasattr(node, 'node_type') and hasattr(node, 'value'):
            node_type_name = node.node_type.name if hasattr(node.node_type, 'name') else str(node.node_type)

            if node_type_name in ['OPERATOR', 'FUNCTION', 'ASSIGNMENT']:
                node_details.append(f"{node_type_name.lower()}({node.value})")

        # 递归处理子节点
        if hasattr(node, 'children'):
            for child in node.children:
                self._extract_node_details(child, node_details)



    def get_key_metrics(self, result: Dict) -> Dict:
        """获取关键指标"""
        precise_data = result['analysis']
        overall_stats = precise_data['overall_statistics']

        return {
            'total_lines': overall_stats['total_lines'],
            'total_variables': overall_stats['total_variables'],
            'total_operations': overall_stats['total_operations'],
            'total_temp_variables': overall_stats['total_temp_variables'],
            'total_type_conversions': overall_stats['total_type_conversions'],
            'total_precision_issues': overall_stats['total_precision_issues'],
            'conversion_ratio': overall_stats['total_type_conversions'] / overall_stats['total_operations'] if overall_stats['total_operations'] > 0 else 0
        }

    def get_optimization_suggestions(self, result: Dict) -> List[str]:
        """获取优化建议列表"""
        precise_data = result['analysis']
        overall_stats = precise_data['overall_statistics']
        suggestions = []

        total_conversions = overall_stats['total_type_conversions']
        total_operations = overall_stats['total_operations']
        precision_issues = overall_stats['total_precision_issues']
        total_temp_vars = overall_stats['total_temp_variables']

        if total_conversions > 10:
            suggestions.append(f"发现 {total_conversions} 个类型转换，建议统一变量类型以减少转换")

        if precision_issues > 0:
            suggestions.append(f"发现 {precision_issues} 个精度问题，可能影响渲染质量")

        conversion_ratio = total_conversions / total_operations if total_operations > 0 else 0
        if conversion_ratio > 0.2:
            suggestions.append(f"类型转换比例过高({conversion_ratio:.1%})，建议重构数据类型设计")

        if total_temp_vars > total_operations * 0.8:
            suggestions.append(f"临时变量较多({total_temp_vars})，可能存在复杂表达式，建议简化")

        if total_operations > 100:
            suggestions.append(f"运算操作较多({total_operations})，建议检查是否有重复计算")

        if not suggestions:
            suggestions.append("代码质量良好，语法树分析未发现明显问题")

        return suggestions

    def _make_serializable(self, obj, visited=None):
        """将对象转换为可序列化的格式"""
        if visited is None:
            visited = set()

        # 防止循环引用
        obj_id = id(obj)
        if obj_id in visited:
            return f"<circular reference to {type(obj).__name__}>"

        if hasattr(obj, '__dict__'):
            visited.add(obj_id)
            # 对象转换为字典
            result = {'__type__': type(obj).__name__}
            for key, value in obj.__dict__.items():
                # 跳过方法和函数
                if callable(value):
                    continue
                # 跳过一些可能导致循环引用的属性
                if key in ['parent', 'children'] and hasattr(value, '__iter__'):
                    # 对于children列表，只保留基本信息
                    if key == 'children' and isinstance(value, list):
                        result[key] = [{'type': type(child).__name__, 'value': getattr(child, 'value', str(child))} for child in value]
                    else:
                        result[key] = f"<{key}: {len(value) if hasattr(value, '__len__') else 'object'}>"
                else:
                    result[key] = self._make_serializable(value, visited)
            visited.remove(obj_id)
            return result
        elif isinstance(obj, dict):
            # 字典递归处理
            return {key: self._make_serializable(value, visited) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            # 列表/元组递归处理
            return [self._make_serializable(item, visited) for item in obj]
        elif hasattr(obj, 'value'):
            # 枚举类型
            return obj.value
        elif hasattr(obj, 'name'):
            # 有name属性的对象
            return obj.name
        else:
            # 基本类型或字符串
            return obj
