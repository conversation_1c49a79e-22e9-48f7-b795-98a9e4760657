from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton, QLabel, qApp
from PyQt5.QtCore import pyqtSignal

class ProcessWidgetBase(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QVBoxLayout(self)

        # 设置紧凑的布局
        self.layout.setSpacing(6)  # 设置合适的间距
        self.layout.setContentsMargins(8, 8, 8, 8)  # 设置合适的边距

        self.label = QLabel('算法窗口')
        self.btn_run = QPushButton('执行')
        self.btn_run.clicked.connect(self.on_run_clicked)
        self.layout.addWidget(self.label)
        self.layout.addWidget(self.btn_run)
        self.setLayout(self.layout)

    def get_source_text(self):
        # 默认取主窗口当前tab内容
        main_win = qApp.activeWindow()
        if hasattr(main_win, 'code_area_widget'):
            current_widget = main_win.code_area_widget.get_current_widget()
            if current_widget is not None and hasattr(current_widget, 'get_text'):
                return current_widget.get_text()
        return ''

    def on_run_clicked(self):
        src = self.get_source_text()
        result = self.run_algorithm(src)
        main_win = qApp.activeWindow()
        if hasattr(main_win, 'code_area_widget'):
            main_win.code_area_widget.add_code_page('算法结果', result, model='QScintilla')

    def run_algorithm(self, text):
        # 子类重写此方法实现具体算法
        return text 
    