#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码行分析器 - 识别和分析着色器代码中的有效代码行
"""

import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

@dataclass
class CodeLineInfo:
    """简化的代码行信息"""
    line_number: int          # 原始行号
    content: str             # 代码内容

class CodeLineAnalyzer:
    """代码行分析器"""
    
    def __init__(self):
        # 需要跳过的行模式
        self.skip_patterns = [
            r'^\s*$',                    # 空行
            r'^\s*//.*$',                # 注释行
            r'^\s*#.*$',                 # 预处理指令
            r'^\s*\{?\s*$',              # 只有大括号的行
            r'^\s*\}?\s*$',              # 只有大括号的行
            # r'^\s*(if|else|for|while|do)\s*\(',  # 控制结构开始 - 注释掉，让if语句也被识别
            r'^\s*(else)\s*$',           # else关键字
            r'^\s*(struct|class|enum)\s+\w+\s*\{?\s*$',  # 结构体声明
            r'^\s*(vertex|fragment|kernel)\s+\w+.*\[\[.*\]\].*\{?\s*$',  # 函数声明
        ]
        
        # 编译正则表达式以提高性能
        self.compiled_skip_patterns = [re.compile(pattern) for pattern in self.skip_patterns]
    
    def is_code_line(self, line: str) -> bool:
        """判断是否是有效的代码行"""
        line = line.strip()
        
        # 空行直接跳过
        if not line:
            return False
        
        # 检查跳过模式
        for pattern in self.compiled_skip_patterns:
            if pattern.match(line):
                return False
        
        # 检查是否包含赋值、函数调用或return语句
        code_indicators = [
            r'=',                        # 赋值
            r'\w+\s*\(',                # 函数调用
            r'return\s+',               # return语句
            r'\+\+|--',                 # 自增自减
            r'[+\-*/]',                 # 算术运算
        ]
        
        for indicator in code_indicators:
            if re.search(indicator, line):
                return True
        
        return False
        """分析单行代码的变量使用情况"""
        analysis = {
            'total_variables': 0,
            'float_variables': 0,
            'half_variables': 0,
            'type_conversions': 0,
            'variables_by_type': {},
            'type_conversion_details': [],
            'all_variables': []
        }
        
        # 移除注释
        clean_line = re.sub(r'//.*$', '', line)
        
        # 1. 统计类型声明变量
        type_declarations = re.findall(
            r'\b(half|float|half2|half3|half4|float2|float3|float4|float3x3|float4x4)\s+([a-zA-Z_][a-zA-Z0-9_]*)', 
            clean_line
        )
        
        for type_name, var_name in type_declarations:
            analysis['total_variables'] += 1
            analysis['all_variables'].append(var_name)
            
            if type_name.startswith('float'):
                analysis['float_variables'] += 1
            elif type_name.startswith('half'):
                analysis['half_variables'] += 1
            
            if type_name not in analysis['variables_by_type']:
                analysis['variables_by_type'][type_name] = []
            analysis['variables_by_type'][type_name].append(var_name)
        
        # 2. 统计所有变量使用（包括成员访问）
        variable_matches = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', clean_line)
        
        # 过滤掉关键字和函数名
        keywords = {
            'half', 'float', 'half2', 'half3', 'half4', 'float2', 'float3', 'float4',
            'float3x3', 'float4x4', 'return', 'if', 'else', 'for', 'while', 'do',
            'true', 'false', 'void', 'const', 'static', 'inline'
        }
        
        # 函数名模式（通常后面跟括号）
        function_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
        function_names = set(re.findall(function_pattern, clean_line))
        
        unique_variables = set()
        for var in variable_matches:
            var_base = var.split('.')[0]
            if var_base not in keywords and var_base not in function_names:
                unique_variables.add(var)
        
        # 如果没有类型声明，统计所有变量使用
        if not type_declarations:
            analysis['total_variables'] = len(unique_variables)
            analysis['all_variables'] = list(unique_variables)
        else:
            # 如果有类型声明，添加其他使用的变量
            for var in unique_variables:
                if var not in analysis['all_variables']:
                    analysis['total_variables'] += 1
                    analysis['all_variables'].append(var)
        
        # 3. 查找类型转换
        conversion_patterns = [
            r'\b(half|float|half2|half3|half4|float2|float3|float4)\s*\(\s*([^)]+)\s*\)',
        ]
        
        for pattern in conversion_patterns:
            conversions = re.findall(pattern, clean_line)
            for target_type, source_expr in conversions:
                source_expr = source_expr.strip()
                # 检查是否真的是类型转换（不是构造函数调用）
                if not re.match(r'^[\d\.\,\s]+$', source_expr):
                    analysis['type_conversions'] += 1
                    analysis['type_conversion_details'].append({
                        'target_type': target_type,
                        'source_expression': source_expr,
                        'line_position': clean_line.find(f"{target_type}({source_expr})")
                    })
        
        return analysis
    
    def analyze_shader_code(self, shader_content: str) -> List[CodeLineInfo]:
        """分析着色器代码，返回所有有效代码行的基本信息"""
        lines = shader_content.split('\n')
        code_lines = []

        for i, line in enumerate(lines, 1):
            if self.is_code_line(line):
                # 创建简化的代码行信息
                code_line = CodeLineInfo(
                    line_number=i,
                    content=line.strip()
                )
                code_lines.append(code_line)

        return code_lines
    
    # 用于打印查看结果
    def print_analysis_result(self, code_lines: List[CodeLineInfo]) -> str:
        """格式化分析结果为可读文本"""
        print(f"识别到 {len(code_lines)} 行有效代码:")
        print("=" * 60)
        
        for code_line in code_lines:
           print(f"第 {code_line.line_number} 行: {code_line.content}")

    