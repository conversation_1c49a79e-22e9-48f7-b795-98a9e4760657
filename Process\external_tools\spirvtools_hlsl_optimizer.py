"""
基于glslang+SPIRV-Tools的HLSL自动优化器。
依赖：需安装glslangValidator、spirv-opt、spirv-cross等命令行工具。

- glslangValidator: 将HLSL源码编译为SPIR-V中间码
- spirv-opt: 对SPIR-V中间码进行底层优化（如死代码消除、常量折叠、循环展开等）
- spirv-cross: 将优化后的SPIR-V反编译回HLSL源码

用法：
    optimizer = SPIRVToolsHLSLOptimizer()
    optimized_code = optimizer.optimize_code(hlsl_code)

或者带选项：
    options = {
        'optimization_level': 'Os',
        'enable_debug': False,
        'target_env': 'vulkan1.2'
    }
    optimizer = SPIRVToolsHLSLOptimizer(options)
    optimized_code = optimizer.optimize_code(hlsl_code)

注意：
    需保证上述命令行工具在系统PATH中可用。
    优化能力取决于SPIRV-Tools的支持范围，类型自动降精度/升精度需结合其他工具实现。
"""
import tempfile
import subprocess
import os

class SPIRVToolsHLSLOptimizer:
    """
    HLSL底层自动优化器，基于SPIR-V工具链。
    支持死代码消除、常量折叠、循环优化等底层优化。
    """
    def __init__(self, options=None):
        self.options = options or {}
        self.optimization_level = self.options.get('optimization_level', 'O')
        self.enable_debug = self.options.get('enable_debug', False)
        self.target_env = self.options.get('target_env', 'vulkan1.0')

    def optimize_code(self, code: str) -> str:
        """
        输入HLSL代码字符串，自动调用glslangValidator、spirv-opt、spirv-cross进行底层优化，返回优化后的HLSL代码字符串。
        """
        with tempfile.TemporaryDirectory() as tmpdir:
            hlsl_path = os.path.join(tmpdir, 'shader.hlsl')
            spv_path = os.path.join(tmpdir, 'shader.spv')
            opt_spv_path = os.path.join(tmpdir, 'shader_opt.spv')
            out_hlsl_path = os.path.join(tmpdir, 'shader_opt.hlsl')
            # 1. 写入HLSL源码到临时文件
            with open(hlsl_path, 'w', encoding='utf-8') as f:
                f.write(code)
            # 2. HLSL -> SPIR-V
            subprocess.run(['glslangValidator', '-V', hlsl_path, '-o', spv_path], check=True)
            # 3. SPIR-V优化
            opt_args = ['spirv-opt', spv_path, f'-{self.optimization_level}', '-o', opt_spv_path]
            subprocess.run(opt_args, check=True)
            # 4. SPIR-V -> HLSL
            subprocess.run(['spirv-cross', opt_spv_path, '--output', out_hlsl_path, '--hlsl'], check=True)
            # 5. 读取优化后的HLSL代码
            with open(out_hlsl_path, 'r', encoding='utf-8') as f:
                code = f.read()
        return code 