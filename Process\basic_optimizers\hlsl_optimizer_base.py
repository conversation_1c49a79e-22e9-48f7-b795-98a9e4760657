"""
HLSL优化器基类，定义统一的接口和通用功能。
"""
from abc import ABC, abstractmethod
import re
from typing import Dict, List, Set, Tuple, Optional
from enum import Enum

class OptimizationLevel(Enum):
    """优化级别枚举"""
    NONE = 0
    BASIC = 1
    AGGRESSIVE = 2
    MAXIMUM = 3

class HLSLOptimizerBase(ABC):
    """
    HLSL优化器基类，所有优化器都应该继承此类。
    
    提供统一的接口和通用的辅助功能。
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        """
        初始化优化器
        
        Args:
            optimization_level: 优化级别
        """
        self.optimization_level = optimization_level
        self.statistics = {
            'optimizations_applied': 0,
            'lines_processed': 0,
            'functions_processed': 0,
            'variables_processed': 0
        }
    
    @abstractmethod
    def optimize_code(self, code: str) -> str:
        """
        优化HLSL代码的抽象方法，子类必须实现
        
        Args:
            code: 输入的HLSL代码字符串
            
        Returns:
            优化后的HLSL代码字符串
        """
        pass
    
    def get_statistics(self) -> Dict:
        """获取优化统计信息"""
        return self.statistics.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        for key in self.statistics:
            self.statistics[key] = 0
    
    # 通用的辅助方法
    def extract_functions(self, code: str) -> List[Tuple[str, str, int, int]]:
        """
        提取代码中的函数定义
        
        Returns:
            List of (function_name, function_body, start_line, end_line)
        """
        functions = []
        lines = code.splitlines()
        i = 0
        
        # 函数定义正则表达式
        func_pattern = re.compile(r'^\s*(?:inline|static|export)?\s*([a-zA-Z0-9_]+)\s+([a-zA-Z0-9_]+)\s*\([^)]*\)\s*{?')
        
        while i < len(lines):
            line = lines[i]
            match = func_pattern.match(line)
            if match:
                func_name = match.group(2)
                start_line = i
                
                # 找到函数体结束位置
                brace_count = line.count('{') - line.count('}')
                if brace_count == 0 and '{' in line:
                    brace_count = 1
                
                func_lines = [line]
                i += 1
                
                while i < len(lines) and brace_count > 0:
                    line = lines[i]
                    func_lines.append(line)
                    brace_count += line.count('{') - line.count('}')
                    i += 1
                
                end_line = i - 1
                func_body = '\n'.join(func_lines)
                functions.append((func_name, func_body, start_line, end_line))
            else:
                i += 1
        
        return functions
    
    def extract_variables(self, code: str) -> Set[str]:
        """提取代码中声明的变量名"""
        variables = set()
        var_pattern = re.compile(r'\b(?:float|half|int|uint|bool|min16float|min16int)(?:\d+(?:x\d+)?)?(?:\[\d*\])?\s+([a-zA-Z_][a-zA-Z0-9_]*)')
        
        for match in var_pattern.finditer(code):
            variables.add(match.group(1))
        
        return variables
    
    def is_shader_entry_point(self, func_name: str) -> bool:
        """判断是否为着色器入口点函数"""
        entry_points = {'main', 'vsmain', 'psmain', 'csmain', 'gsmain', 'hsmain', 'dsmain'}
        return func_name.lower() in entry_points
    
    def count_instruction_complexity(self, code: str) -> int:
        """估算指令复杂度"""
        complexity = 0
        
        # 数学函数权重
        math_functions = {
            'sin': 8, 'cos': 8, 'tan': 10, 'asin': 12, 'acos': 12, 'atan': 10,
            'exp': 8, 'log': 8, 'pow': 12, 'sqrt': 4, 'rsqrt': 3,
            'normalize': 6, 'length': 4, 'distance': 5, 'dot': 2, 'cross': 4
        }
        
        # 纹理采样权重
        texture_ops = {
            'Sample': 8, 'SampleLevel': 10, 'SampleGrad': 12, 'SampleCmp': 10,
            'Load': 4, 'Gather': 6
        }
        
        # 计算数学函数复杂度
        for func, weight in math_functions.items():
            complexity += len(re.findall(rf'\b{func}\s*\(', code, re.IGNORECASE)) * weight
        
        # 计算纹理操作复杂度
        for op, weight in texture_ops.items():
            complexity += len(re.findall(rf'\.{op}\s*\(', code, re.IGNORECASE)) * weight
        
        # 基础运算复杂度
        complexity += len(re.findall(r'[+\-*/]', code))
        
        return complexity
    
    def get_hlsl_type_size(self, type_name: str) -> int:
        """获取HLSL类型的字节大小"""
        type_sizes = {
            'bool': 4, 'int': 4, 'uint': 4, 'float': 4, 'half': 2, 'double': 8,
            'min16int': 2, 'min16uint': 2, 'min16float': 2,
            'int2': 8, 'int3': 12, 'int4': 16,
            'uint2': 8, 'uint3': 12, 'uint4': 16,
            'float2': 8, 'float3': 12, 'float4': 16,
            'half2': 4, 'half3': 6, 'half4': 8,
            'float2x2': 16, 'float3x3': 36, 'float4x4': 64,
            'half2x2': 8, 'half3x3': 18, 'half4x4': 32
        }
        return type_sizes.get(type_name, 4)
