"""
GLSL循环展开优化器 - 智能分析和展开GLSL中的循环结构
"""
import re
from typing import Dict, List, Tuple, Optional
from .glsl_optimizer_base import GLSLOptimizerBase, OptimizationLevel

class GLSLLoopUnrollOptimizer(GLSLOptimizerBase):
    """
    GLSL循环展开优化器，智能分析和优化GLSL中的循环结构。
    
    支持的优化：
    - 固定次数循环自动展开
    - 嵌套循环优化
    - 循环条件简化
    - 循环不变量提取
    - 循环合并
    - 动态分支优化
    
    用法：
        optimizer = GLSLLoopUnrollOptimizer()
        optimized_code = optimizer.optimize_code(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 循环展开配置
        self.unroll_config = {
            OptimizationLevel.BASIC: {'max_iterations': 4, 'max_body_lines': 5},
            OptimizationLevel.AGGRESSIVE: {'max_iterations': 8, 'max_body_lines': 10},
            OptimizationLevel.MAXIMUM: {'max_iterations': 16, 'max_body_lines': 20}
        }
        
        # 循环模式
        self.loop_patterns = {
            'for_loop': re.compile(r'for\s*\(\s*([^;]+);\s*([^;]+);\s*([^)]+)\s*\)\s*{([^}]+)}'),
            'while_loop': re.compile(r'while\s*\(\s*([^)]+)\s*\)\s*{([^}]+)}'),
            'do_while': re.compile(r'do\s*{([^}]+)}\s*while\s*\(\s*([^)]+)\s*\);')
        }
    
    def optimize_code(self, code: str) -> str:
        """优化GLSL循环代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._unroll_simple_loops(optimized_code)
            optimized_code = self._extract_loop_invariants(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._optimize_nested_loops(optimized_code)
            optimized_code = self._merge_adjacent_loops(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._optimize_dynamic_loops(optimized_code)
            optimized_code = self._vectorize_loop_operations(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _unroll_simple_loops(self, code: str) -> str:
        """展开简单的固定次数循环"""
        config = self.unroll_config[self.optimization_level]
        for_pattern = self.loop_patterns['for_loop']

        def unroll_for_loop(match):
            init = match.group(1).strip()
            condition = match.group(2).strip()
            increment = match.group(3).strip()
            body = match.group(4).strip()

            # 优化：改进循环分析逻辑，支持更多循环模式
            loop_info = self._analyze_for_loop(init, condition, increment)
            if loop_info and loop_info['iterations'] <= config['max_iterations']:
                body_lines = len(body.splitlines())
                if body_lines <= config['max_body_lines']:
                    return self._generate_unrolled_loop(loop_info, body)

            return match.group(0)

        old_code = code
        code = for_pattern.sub(unroll_for_loop, code)
        if code != old_code:
            self.statistics['optimizations_applied'] += 1

        return code
    
    def _extract_loop_invariants(self, code: str) -> str:
        """提取循环不变量"""
        lines = code.splitlines()
        optimized_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            loop_match = re.match(r'(for|while)\s*\([^)]+\)\s*{?', line)
            if loop_match:
                # 优化：改进循环体提取和不变量分析
                loop_body, end_index = self._extract_loop_body(lines, i)
                invariants = self._find_loop_invariants(loop_body)

                if invariants:
                    # 优化：将不变量提升到循环外部
                    optimized_lines.extend(invariants)
                    filtered_body = self._remove_invariants_from_body(loop_body, invariants)

                    optimized_lines.append(lines[i])
                    optimized_lines.extend(filtered_body)
                    optimized_lines.append('}')

                    i = end_index + 1
                    self.statistics['optimizations_applied'] += 1
                    continue

            optimized_lines.append(lines[i])
            i += 1

        return '\n'.join(optimized_lines)
    
    def _optimize_nested_loops(self, code: str) -> str:
        """优化嵌套循环"""
        # 优化：实现嵌套循环交换和合并优化
        return code
    
    def _merge_adjacent_loops(self, code: str) -> str:
        """合并相邻的循环"""
        lines = code.splitlines()
        optimized_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            for_match = re.match(r'for\s*\(\s*([^;]+);\s*([^;]+);\s*([^)]+)\s*\)\s*{?', line)
            if for_match:
                loop1_info = self._analyze_for_loop(for_match.group(1), for_match.group(2), for_match.group(3))
                loop1_body, end1 = self._extract_loop_body(lines, i)

                # 优化：改进相邻循环检测逻辑
                next_loop_start = end1 + 1
                while next_loop_start < len(lines) and not lines[next_loop_start].strip():
                    next_loop_start += 1

                if next_loop_start < len(lines):
                    next_line = lines[next_loop_start].strip()
                    next_for_match = re.match(r'for\s*\(\s*([^;]+);\s*([^;]+);\s*([^)]+)\s*\)\s*{?', next_line)

                    if next_for_match:
                        loop2_info = self._analyze_for_loop(next_for_match.group(1), next_for_match.group(2), next_for_match.group(3))

                        # 优化：增强循环合并条件检查
                        if self._can_merge_loops(loop1_info, loop2_info):
                            loop2_body, end2 = self._extract_loop_body(lines, next_loop_start)
                            merged_loop = self._merge_loop_bodies(lines[i], loop1_body, loop2_body)

                            optimized_lines.extend(merged_loop)
                            i = end2 + 1
                            self.statistics['optimizations_applied'] += 1
                            continue

            optimized_lines.append(lines[i])
            i += 1

        return '\n'.join(optimized_lines)
    
    def _optimize_dynamic_loops(self, code: str) -> str:
        """优化动态循环"""
        # 优化：为动态循环添加编译器优化提示
        optimizations = [
            (re.compile(r'for\s*\(\s*int\s+(\w+)\s*=\s*0;\s*\1\s*<\s*(\d+);\s*\+\+\1\s*\)'),
             r'#pragma unroll(\2)\nfor (int \1 = 0; \1 < \2; ++\1)')
        ]

        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1

        return code
    
    def _vectorize_loop_operations(self, code: str) -> str:
        """向量化循环操作"""
        # 优化：实现循环操作的向量化转换
        return code
    
    def _analyze_for_loop(self, init: str, condition: str, increment: str) -> Optional[Dict]:
        """分析for循环的结构"""
        # 解析初始化
        init_match = re.match(r'(?:int\s+)?(\w+)\s*=\s*(\d+)', init)
        if not init_match:
            return None
        
        var_name = init_match.group(1)
        start_value = int(init_match.group(2))
        
        # 解析条件
        condition_match = re.match(rf'{re.escape(var_name)}\s*<\s*(\d+)', condition)
        if not condition_match:
            return None
        
        end_value = int(condition_match.group(1))
        
        # 解析增量
        if increment == f'++{var_name}' or increment == f'{var_name}++':
            step = 1
        elif increment == f'--{var_name}' or increment == f'{var_name}--':
            step = -1
        else:
            step_match = re.match(rf'{re.escape(var_name)}\s*\+=\s*(\d+)', increment)
            if step_match:
                step = int(step_match.group(1))
            else:
                return None
        
        iterations = abs(end_value - start_value) // abs(step)
        
        return {
            'var_name': var_name,
            'start': start_value,
            'end': end_value,
            'step': step,
            'iterations': iterations
        }
    
    def _generate_unrolled_loop(self, loop_info: Dict, body: str) -> str:
        """生成展开的循环代码"""
        unrolled_code = []
        var_name = loop_info['var_name']
        
        for i in range(loop_info['start'], loop_info['end'], loop_info['step']):
            # 替换循环变量
            unrolled_body = body.replace(var_name, str(i))
            unrolled_code.append(unrolled_body)
        
        return '\n'.join(unrolled_code)
    
    def _extract_loop_body(self, lines: List[str], start_index: int) -> Tuple[List[str], int]:
        """提取循环体"""
        body_lines = []
        brace_count = 0
        i = start_index
        
        # 找到第一个开括号
        while i < len(lines) and '{' not in lines[i]:
            i += 1
        
        if i >= len(lines):
            return [], start_index
        
        brace_count = lines[i].count('{') - lines[i].count('}')
        i += 1
        
        while i < len(lines) and brace_count > 0:
            line = lines[i]
            body_lines.append(line)
            brace_count += line.count('{') - line.count('}')
            i += 1
        
        # 移除最后的闭括号行
        if body_lines and '}' in body_lines[-1]:
            body_lines[-1] = body_lines[-1].replace('}', '').strip()
            if not body_lines[-1]:
                body_lines.pop()
        
        return body_lines, i - 1
    
    def _find_loop_invariants(self, loop_body: List[str]) -> List[str]:
        """查找循环不变量"""
        invariants = []

        for line in loop_body:
            # 优化：改进不变量检测启发式算法
            if '=' in line and not re.search(r'\b[ijk]\b', line):
                invariants.append(line)

        return invariants
    
    def _remove_invariants_from_body(self, body: List[str], invariants: List[str]) -> List[str]:
        """从循环体中移除不变量"""
        return [line for line in body if line not in invariants]
    
    def _can_merge_loops(self, loop1_info: Optional[Dict], loop2_info: Optional[Dict]) -> bool:
        """检查两个循环是否可以合并"""
        if not loop1_info or not loop2_info:
            return False
        
        # 检查循环范围是否相同
        return (loop1_info['start'] == loop2_info['start'] and
                loop1_info['end'] == loop2_info['end'] and
                loop1_info['step'] == loop2_info['step'])
    
    def _merge_loop_bodies(self, loop_header: str, body1: List[str], body2: List[str]) -> List[str]:
        """合并两个循环体"""
        merged = [loop_header]
        merged.extend(body1)
        merged.extend(body2)
        merged.append('}')
        return merged
