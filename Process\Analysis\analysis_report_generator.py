#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器 - 基于AST的精确分析报告
"""

import json
import os
import re
import tempfile
from typing import Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass
from .templates.html_template import HTML_TEMPLATE_HEADER, HTML_TEMPLATE_FOOTER
@dataclass
class CodeLineData:
    """代码行数据结构"""
    line_number: int
    content: str
    operation_count: int = 0
    conversion_count: int = 0
    precision_issues: int = 0
    node_details: List[str] = None
    has_analysis: bool = False
    css_classes: List[str] = None
    performance_level: str = "normal"  # normal, intensive, conversion, issue

    def __post_init__(self):
        if self.node_details is None:
            self.node_details = []
        if self.css_classes is None:
            self.css_classes = []

    def get_stats_text(self) -> str:
        """获取统计信息文本"""
        if not self.has_analysis or self.operation_count == 0:
            return ""

        stats = f"运算: {self.operation_count}次"
        if self.conversion_count > 0:
            stats += f" | 类型转换: {self.conversion_count}次"

        return stats

    def get_color_style(self, use_colors: bool = True) -> str:
        """获取颜色样式"""
        if not use_colors or not self.has_analysis:
            return ""

        if self.conversion_count > 0:
            return "border-left: 3px solid #f44336;"  # 红色 - 类型转换
        elif self.operation_count > 5:
            return "border-left: 3px solid #ff9800;"  # 橙色 - 运算密集
        elif self.operation_count > 0:
            return "border-left: 3px solid #4caf50;"  # 绿色 - 正常运算

        return ""

class AnalysisReportGenerator:
    """报告生成器 - 基于AST的精确分析报告"""

    def __init__(self):
        self.temp_dir = tempfile.gettempdir()

    def generate_precise_html_report(self, precise_data: Dict, code_lines_data: List[CodeLineData]) -> str:
        """生成基于语法树的HTML报告"""
        overall_stats = precise_data['overall_statistics']

        # 计算统计数据
        total_lines = len(code_lines_data)
        analyzed_lines = sum(1 for line in code_lines_data if line.has_analysis)
        total_operations = overall_stats.get('total_operations', 0)
        total_conversions = overall_stats.get('total_type_conversions', 0)
        total_precision_issues = overall_stats.get('total_precision_issues', 0)
        total_variables = overall_stats.get('total_variables', 0)
        total_temp_variables = overall_stats.get('total_temp_variables', 0)
        analysis_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 使用模板生成HTML头部
        html_content = HTML_TEMPLATE_HEADER.format(
            total_lines=total_lines,
            analysis_time=analysis_time,
            total_nodes=total_variables + total_temp_variables,  # 使用变量总数作为节点数
            analyzed_lines=analyzed_lines,
            total_operations=total_operations,
            total_conversions=total_conversions,
            total_precision_issues=total_precision_issues
        )
        # 生成所有代码行
        for line_data in code_lines_data:
            css_classes = []

            # 添加基础CSS类
            if line_data.css_classes:
                css_classes.extend(line_data.css_classes)

            # 添加性能级别类
            if line_data.has_analysis and line_data.operation_count > 0:
                css_classes.append(f"performance-{line_data.performance_level}")

            # 数据属性
            data_attrs = []
            data_attrs.append(f'data-has-analysis="{str(line_data.has_analysis).lower()}"')
            data_attrs.append(f'data-performance="{line_data.performance_level}"')
            data_attrs.append(f'data-operations="{line_data.operation_count}"')
            data_attrs.append(f'data-conversions="{line_data.conversion_count}"')

            css_class_str = ' '.join(css_classes)
            data_attr_str = ' '.join(data_attrs)

            # 生成详细的分析信息
            analysis_details = self._generate_line_analysis_details(line_data, precise_data)

            html_content += f"""
            <div class="code-line {css_class_str}" {data_attr_str}>
                <div class="line-number">{line_data.line_number}</div>
                <div class="line-content">
                    <div class="code-text">{self._escape_html(line_data.content)}</div>
                    <div class="analysis-info">
                        <div class="basic-stats">{line_data.get_stats_text()}</div>
                        {analysis_details}
                    </div>
                </div>
            </div>
            """

        # 添加HTML尾部
        html_content += HTML_TEMPLATE_FOOTER

        return html_content

    def _generate_line_analysis_details(self, line_data: CodeLineData, precise_data: Dict) -> str:
        """生成单行代码的详细分析信息"""
        if not line_data.has_analysis:
            return ""

        details_html = ""

        # 查找对应的行分析结果
        line_analysis = None
        if 'line_analyses' in precise_data:
            for analysis in precise_data['line_analyses']:
                if hasattr(analysis, 'line_number') and analysis.line_number == line_data.line_number:
                    line_analysis = analysis
                    break

        if not line_analysis:
            return ""

        # 运算过程详情
        if hasattr(line_analysis, 'operation_process') and line_analysis.operation_process:
            details_html += '<div class="operation-process">'
            details_html += '<h4>运算过程:</h4>'
            details_html += '<ul class="operation-list">'

            for i, op in enumerate(line_analysis.operation_process, 1):
                op_string = self._escape_html(op.string) if hasattr(op, 'string') else str(op)
                left_types = []
                right_types = []

                if hasattr(op, 'left_dataType') and op.left_dataType:
                    left_types = [t.value if hasattr(t, 'value') else str(t) for t in op.left_dataType]
                if hasattr(op, 'right_dataType') and op.right_dataType:
                    right_types = [t.value if hasattr(t, 'value') else str(t) for t in op.right_dataType]

                details_html += f'<li class="operation-step">'
                details_html += f'<span class="step-number">{i}.</span> '
                details_html += f'<code class="operation-code">{op_string}</code>'

                if left_types or right_types:
                    details_html += '<div class="type-info">'
                    if left_types:
                        details_html += f'<span class="left-types">左: {", ".join(left_types)}</span>'
                    if right_types:
                        details_html += f'<span class="right-types">右: {", ".join(right_types)}</span>'
                    details_html += '</div>'

                details_html += '</li>'

            details_html += '</ul></div>'

        # 类型转换详情
        if hasattr(line_analysis, 'type_conversions') and line_analysis.type_conversions:
            details_html += '<div class="type-conversions">'
            details_html += '<h4>类型转换:</h4>'
            details_html += '<ul class="conversion-list">'

            for conversion in line_analysis.type_conversions:
                conv_type = conversion.get('conversion_type', 'unknown')
                from_type = conversion.get('from_type', 'unknown')
                to_type = conversion.get('to_type', 'unknown')
                operation = conversion.get('operation', '')

                css_class = self._get_conversion_css_class(conv_type)
                details_html += f'<li class="conversion-item {css_class}">'
                details_html += f'<span class="conversion-types">{from_type} → {to_type}</span>'
                details_html += f'<span class="conversion-type">({conv_type})</span>'
                if operation:
                    details_html += f'<div class="conversion-operation">在: {self._escape_html(operation)}</div>'
                details_html += '</li>'

            details_html += '</ul></div>'

        # 变量类型信息
        if hasattr(line_analysis, 'variable_types') and line_analysis.variable_types:
            details_html += '<div class="variable-types">'
            details_html += '<h4>变量类型:</h4>'
            details_html += '<ul class="variable-list">'

            for var_name, var_type in line_analysis.variable_types.items():
                type_name = var_type.value if hasattr(var_type, 'value') else str(var_type)
                details_html += f'<li class="variable-item">'
                details_html += f'<span class="var-name">{var_name}</span>: '
                details_html += f'<span class="var-type">{type_name}</span>'
                details_html += '</li>'

            details_html += '</ul></div>'

        return details_html

    def _escape_html(self, text: str) -> str:
        """转义HTML特殊字符"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))

    def _get_conversion_css_class(self, conversion_type: str) -> str:
        """获取类型转换的CSS类"""
        if 'precision_loss' in conversion_type:
            return 'precision-loss'
        elif 'precision_gain' in conversion_type:
            return 'precision-gain'
        elif 'vector' in conversion_type:
            return 'vector-conversion'
        else:
            return 'implicit-conversion'