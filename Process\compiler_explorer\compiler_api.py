import requests
import json
from typing import List, Tuple, Optional, Dict, Any

GODBOLT_API_URL = "https://godbolt.org/api/compiler/g122/compile"


class CompilerExplorerAPI:
    """
    Compiler Explorer API 静态工具类
    包含所有与编译器相关的逻辑功能
    """

    _compiler_list_cache: Optional[List[Tuple[str, str]]] = None  # 编译器列表缓存

    @staticmethod
    def fetch_compiler_list() -> List[Tuple[str, str]]:
        """
        获取编译器列表
        :return: [(编译器名称, 编译器ID), ...] 的列表
        """
        if CompilerExplorerAPI._compiler_list_cache is not None:
            return CompilerExplorerAPI._compiler_list_cache

        try:
            resp = requests.get(
                'https://godbolt.org/api/compilers',
                timeout=10,
                headers={'Accept': 'application/json'}
            )
            resp.raise_for_status()
            filtered = []

            if 'application/json' in resp.headers.get('Content-Type', ''):
                compilers = resp.json()
                filtered = [
                    (c['name'], c['id'])
                    for c in compilers
                    if any(x in c['id'].lower() or x in c['name'].lower() for x in ['dxc', 'rga'])
                ]
            else:
                # fallback: 解析纯文本
                lines = resp.text.strip().splitlines()
                for line in lines:
                    if '|' in line:
                        cid, name = line.split('|', 1)
                        if any(x in cid.lower() or x in name.lower() for x in ['dxc', 'rga']):
                            filtered.append((name.strip(), cid.strip()))

            if not filtered:
                filtered = [('未找到dxc/rga编译器', '')]

            CompilerExplorerAPI._compiler_list_cache = filtered  # 缓存结果
            return filtered

        except Exception as e:
            print('获取编译器失败:', e)
            return [('获取失败', '')]

    @staticmethod
    def compile_hlsl_source_with_compiler(source_code: str, compiler_id: str, options: Dict[str, Any]) -> str:
        """
        使用指定编译器编译HLSL源码
        :param source_code: HLSL源码字符串
        :param compiler_id: 编译器ID
        :param options: 编译选项字典
        :return: 编译结果字符串
        """
        try:
            url = f'https://godbolt.org/api/compiler/{compiler_id}/compile'
            payload = {"source": source_code, "options": options}
            headers = {"Content-Type": "application/json"}

            resp = requests.post(url, json=payload, headers=headers, timeout=15)
            resp.raise_for_status()

            try:
                data = resp.json()
                asm = data.get("asm", "")
                if isinstance(asm, list):
                    result = "\n".join([line.get("text", "") for line in asm])
                else:
                    result = str(asm)
                return result
            except Exception:
                # 直接返回原始文本
                return resp.text

        except Exception as e:
            return f"[API调用失败] {e}"

    @staticmethod
    def parse_options(options_text: str) -> Dict[str, Any]:
        """
        解析编译选项文本为字典格式
        :param options_text: 编译选项文本
        :return: 编译选项字典
        """
        try:
            options = json.loads(options_text)
            if not isinstance(options, dict):
                options = {"userArguments": options_text}
        except Exception:
            options = {"userArguments": options_text}
        return options

    @staticmethod
    def test_api_connection() -> str:
        """
        测试API连接
        :return: 测试结果字符串
        """
        compiler_id = 'dxc_trunk'
        options = {"userArguments": "-T ps_6_0 -E main"}
        hlsl_code = 'float4 main(float2 uv : TEXCOORD) : SV_Target { return float4(uv, 0, 1); }'

        return CompilerExplorerAPI.compile_hlsl_source_with_compiler(hlsl_code, compiler_id, options)

    @staticmethod
    def create_compiler_name_id_map(compiler_list: List[Tuple[str, str]]) -> Dict[str, str]:
        """
        创建编译器名称到ID的映射
        :param compiler_list: 编译器列表
        :return: 名称到ID的映射字典
        """
        return {name: cid for name, cid in compiler_list}

