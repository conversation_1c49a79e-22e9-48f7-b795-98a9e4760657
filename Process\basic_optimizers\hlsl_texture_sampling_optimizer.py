"""
HLSL纹理采样优化器 - 优化HLSL中的纹理采样操作
"""
import re
from typing import Dict, List, Tuple, Set
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel

class HLSLTextureSamplingOptimizer(HLSLOptimizerBase):
    """
    纹理采样优化器，专门优化HLSL中的纹理采样操作。
    
    支持的优化：
    - 采样器状态合并
    - LOD计算优化
    - 纹理格式转换建议
    - 采样模式优化
    - 重复采样消除
    - 采样器缓存优化
    
    用法：
        optimizer = TextureSamplingOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 纹理采样模式映射
        self.sampling_patterns = {
            # 基础采样
            'sample': re.compile(r'(\w+)\.Sample\s*\(\s*(\w+)\s*,\s*([^)]+)\)'),
            'sample_level': re.compile(r'(\w+)\.SampleLevel\s*\(\s*(\w+)\s*,\s*([^,]+)\s*,\s*([^)]+)\)'),
            'sample_grad': re.compile(r'(\w+)\.SampleGrad\s*\(\s*(\w+)\s*,\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^)]+)\)'),
            'sample_cmp': re.compile(r'(\w+)\.SampleCmp\s*\(\s*(\w+)\s*,\s*([^,]+)\s*,\s*([^)]+)\)'),
            'load': re.compile(r'(\w+)\.Load\s*\(\s*([^)]+)\)'),
            'gather': re.compile(r'(\w+)\.Gather\s*\(\s*(\w+)\s*,\s*([^)]+)\)')
        }
        
        # 采样器状态优化
        self.sampler_states = {}
        self.texture_usage = {}
    
    def optimize_code(self, code: str) -> str:
        """优化纹理采样代码"""
        self.reset_statistics()
        
        # 分析纹理和采样器使用情况
        self._analyze_texture_usage(code)
        
        # 应用各种优化
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._optimize_redundant_sampling(optimized_code)
            optimized_code = self._optimize_lod_calculations(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._merge_sampler_states(optimized_code)
            optimized_code = self._optimize_sampling_patterns(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._optimize_texture_formats(optimized_code)
            optimized_code = self._vectorize_sampling(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _analyze_texture_usage(self, code: str):
        """分析纹理和采样器的使用情况"""
        lines = code.splitlines()
        
        for line_num, line in enumerate(lines):
            # 查找纹理声明
            tex_decl = re.search(r'(Texture\d*D|TextureCube|Texture\d*DArray)\s*<\s*([^>]+)\s*>\s+(\w+)', line)
            if tex_decl:
                tex_type, format_type, tex_name = tex_decl.groups()
                self.texture_usage[tex_name] = {
                    'type': tex_type,
                    'format': format_type,
                    'samples': [],
                    'line': line_num
                }
            
            # 查找采样器声明
            sampler_decl = re.search(r'SamplerState\s+(\w+)', line)
            if sampler_decl:
                sampler_name = sampler_decl.group(1)
                self.sampler_states[sampler_name] = {
                    'usage_count': 0,
                    'textures': set(),
                    'line': line_num
                }
            
            # 记录采样操作
            for pattern_name, pattern in self.sampling_patterns.items():
                matches = pattern.finditer(line)
                for match in matches:
                    if pattern_name in ['sample', 'sample_level', 'sample_grad', 'sample_cmp', 'gather']:
                        tex_name = match.group(1)
                        sampler_name = match.group(2)
                        
                        if tex_name in self.texture_usage:
                            self.texture_usage[tex_name]['samples'].append({
                                'type': pattern_name,
                                'sampler': sampler_name,
                                'line': line_num,
                                'coords': match.group(3) if len(match.groups()) >= 3 else None
                            })
                        
                        if sampler_name in self.sampler_states:
                            self.sampler_states[sampler_name]['usage_count'] += 1
                            self.sampler_states[sampler_name]['textures'].add(tex_name)
    
    def _optimize_redundant_sampling(self, code: str) -> str:
        """消除重复的纹理采样"""
        lines = code.splitlines()
        optimized_lines = []
        sample_cache = {}  # 缓存采样结果
        
        for line in lines:
            modified_line = line
            
            # 查找采样操作
            for pattern_name, pattern in self.sampling_patterns.items():
                matches = list(pattern.finditer(line))
                
                for match in reversed(matches):  # 从后往前替换，避免位置偏移
                    sample_key = match.group(0)
                    
                    # 检查是否已经有相同的采样
                    if sample_key in sample_cache:
                        # 替换为缓存的变量
                        cache_var = sample_cache[sample_key]
                        modified_line = modified_line[:match.start()] + cache_var + modified_line[match.end():]
                        self.statistics['optimizations_applied'] += 1
                    else:
                        # 为新的采样创建缓存变量
                        cache_var = f"_cached_sample_{len(sample_cache)}"
                        sample_cache[sample_key] = cache_var
                        
                        # 在当前行之前插入缓存声明（使用明确的类型而不是auto）
                        cache_decl = f"    float4 {cache_var} = {sample_key};"
                        optimized_lines.append(cache_decl)
                        
                        # 替换当前采样为缓存变量
                        modified_line = modified_line[:match.start()] + cache_var + modified_line[match.end():]
            
            optimized_lines.append(modified_line)
        
        return '\n'.join(optimized_lines)
    
    def _optimize_lod_calculations(self, code: str) -> str:
        """优化LOD计算"""
        optimizations = [
            # SampleLevel(sampler, uv, 0) -> Sample(sampler, uv)
            (re.compile(r'(\w+)\.SampleLevel\s*\(\s*(\w+)\s*,\s*([^,]+)\s*,\s*0(?:\.0)?\s*\)'),
             r'\1.Sample(\2, \3)'),
            
            # 预计算LOD值
            (re.compile(r'(\w+)\.SampleLevel\s*\(\s*(\w+)\s*,\s*([^,]+)\s*,\s*log2\(([^)]+)\)\s*\)'),
             r'\1.SampleLevel(\2, \3, log2(\4))'),
            
            # 使用SampleBias替代SampleLevel的某些情况
            (re.compile(r'(\w+)\.SampleLevel\s*\(\s*(\w+)\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\+\s*1\s*\)'),
             r'\1.SampleBias(\2, \3, 1.0)')
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _merge_sampler_states(self, code: str) -> str:
        """合并相似的采样器状态"""
        # 查找可以合并的采样器
        mergeable_samplers = {}
        
        for sampler_name, sampler_info in self.sampler_states.items():
            if sampler_info['usage_count'] == 1:
                # 使用次数少的采样器可以考虑合并
                for other_sampler, other_info in self.sampler_states.items():
                    if (other_sampler != sampler_name and 
                        other_info['textures'] == sampler_info['textures']):
                        mergeable_samplers[sampler_name] = other_sampler
                        break
        
        # 应用采样器合并
        optimized_code = code
        for old_sampler, new_sampler in mergeable_samplers.items():
            optimized_code = re.sub(rf'\b{old_sampler}\b', new_sampler, optimized_code)
            self.statistics['optimizations_applied'] += 1
        
        return optimized_code
    
    def _optimize_sampling_patterns(self, code: str) -> str:
        """优化采样模式"""
        optimizations = [
            # 使用Gather优化多次相邻采样
            # 这里是一个简化的示例，实际实现会更复杂
            (re.compile(r'(\w+)\.Sample\s*\(\s*(\w+)\s*,\s*([^)]+)\s*\)\.r'),
             r'\1.Gather(\2, \3).x'),
            
            # 优化纹理坐标计算
            (re.compile(r'(\w+)\.Sample\s*\(\s*(\w+)\s*,\s*([^)]+)\s*\*\s*1\.0\s*\)'),
             r'\1.Sample(\2, \3)'),
            
            # 消除不必要的swizzle
            (re.compile(r'(\w+)\.Sample\s*\(\s*(\w+)\s*,\s*([^)]+)\s*\)\.rgba'),
             r'\1.Sample(\2, \3)')
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _optimize_texture_formats(self, code: str) -> str:
        """优化纹理格式建议（添加注释）"""
        optimized_lines = []
        
        for line in code.splitlines():
            optimized_lines.append(line)
            
            # 检查纹理格式声明
            tex_decl = re.search(r'(Texture\d*D|TextureCube)\s*<\s*(float4|half4|float3|half3|float2|half2|float|half)\s*>\s+(\w+)', line)
            if tex_decl:
                tex_type, format_type, tex_name = tex_decl.groups()
                
                # 根据使用情况建议格式优化
                if tex_name in self.texture_usage:
                    usage = self.texture_usage[tex_name]
                    
                    # 如果只使用了RGB通道，建议使用RGB格式
                    if format_type == 'float4' and self._only_uses_rgb(usage):
                        optimized_lines.append(f"// OPTIMIZATION: Consider using float3 for {tex_name} (only RGB channels used)")
                    
                    # 如果精度要求不高，建议使用half
                    elif format_type.startswith('float') and self._can_use_half_precision(usage):
                        half_format = format_type.replace('float', 'half')
                        optimized_lines.append(f"// OPTIMIZATION: Consider using {half_format} for {tex_name} (half precision sufficient)")
        
        return '\n'.join(optimized_lines)
    
    def _vectorize_sampling(self, code: str) -> str:
        """向量化纹理采样操作"""
        # 查找可以向量化的采样模式
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 查找连续的相似采样操作
            if i + 1 < len(lines):
                current_sample = re.search(r'(\w+)\s*=\s*(\w+)\.Sample\s*\(\s*(\w+)\s*,\s*([^)]+)\s*\)\.(\w)', line)
                next_sample = re.search(r'(\w+)\s*=\s*(\w+)\.Sample\s*\(\s*(\w+)\s*,\s*([^)]+)\s*\)\.(\w)', lines[i + 1])
                
                if (current_sample and next_sample and 
                    current_sample.group(2) == next_sample.group(2) and  # 同一纹理
                    current_sample.group(3) == next_sample.group(3)):    # 同一采样器
                    
                    # 可以向量化
                    tex_name = current_sample.group(2)
                    sampler_name = current_sample.group(3)
                    coords = current_sample.group(4)
                    
                    var1, channel1 = current_sample.group(1), current_sample.group(5)
                    var2, channel2 = next_sample.group(1), next_sample.group(5)
                    
                    # 生成向量化的采样
                    vectorized_line = f"    float2 _vec_sample = {tex_name}.Sample({sampler_name}, {coords}).{channel1}{channel2};"
                    optimized_lines.append(vectorized_line)
                    optimized_lines.append(f"    {var1} = _vec_sample.x;")
                    optimized_lines.append(f"    {var2} = _vec_sample.y;")
                    
                    self.statistics['optimizations_applied'] += 1
                    i += 2  # 跳过下一行
                    continue
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _only_uses_rgb(self, usage_info: Dict) -> bool:
        """检查是否只使用了RGB通道"""
        # 简化的实现，实际需要更复杂的分析
        return len(usage_info['samples']) > 0
    
    def _can_use_half_precision(self, usage_info: Dict) -> bool:
        """检查是否可以使用半精度"""
        # 简化的实现，实际需要分析精度需求
        return True
