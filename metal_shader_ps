#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

struct _Block1T
{
    float4 AerialPerspectiveExt;
    float4 AerialPerspectiveMie;
    float4 AerialPerspectiveRay;
    float4 AmbientColor;
    float4 CSMCacheIndexs;
    float4 CSMShadowBiases;
    float4 CameraInfo;
    float4 CameraPos;
    float4 DiyLightingInfo;
    float4 EnvInfo;
    float4 FogColor;
    float4 FogInfo;
    float4 GIInfo;
    float4 HexRenderOptionData[4];
    float4 LightDataBuffer[65];
    float3x4 Local;
    float4 OriginSunDir;
    float4 PlayerPos;
    float4 ReflectionProbeBBMin;
    float4 SHAOParam;
    float4 SHGIParam;
    float4 SHGIParam2;
    float4 ScreenInfo;
    float4 ScreenMotionGray;
    float4x4 ShadowViewProjTexs0;
    float4x4 ShadowViewProjTexs1;
    float4x4 ShadowViewProjTexs2;
    float4 SunColor;
    float4 SunDirection;
    float4 SunFogColor;
    float4 TimeOfDayInfos;
    float3x4 World;
    float4 WorldProbeInfo;
    float4 cCIFadeTime;
    float4 cCISnowData;
    float4 cCISwitchData;
    float4 cLocalVirtualLitColor;
    float4 cLocalVirtualLitCustom;
    float4 cLocalVirtualLitPos;
    float4 cSHCoefficients[7];
    float4 cShadowBias;
    float4 cVirtualLitColor;
    float4 cVirtualLitParam;
    float4 cVisibilitySH[2];
    packed_float3 cBaseColor;
    float eIsPlayerOverride;
    packed_float3 cCIMudBuff;
    float eFresnelPower;
    packed_float3 cEmissionColor;
    float eFresnelMinIntensity;
    packed_float3 eFresnelColor;
    float eFresnelIntensity;
    float cAOoffset;
    float cBiasFarAwayShadow;
    float cEmissionScale;
    float cFurFadeInt;
    float cMicroShadow;
    float cNoise1Scale;
    float cNoise2Bias;
    float cNoise2Scale;
    float cNormalMapStrength;
    float cSaturation;
    float eDynamicFresnelIntensity;
    float eFresnelAlphaAdd;
};

constant half3 _18526 = {};
constant float3 _19185 = {};
constant half _19493 = {};
constant float _19585 = {};
constant int _19621 = {};
constant float3 _21234 = {};
constant float3 _21295 = {};
constant half4 _21296 = {};
constant half3 _21297 = {};

struct main0_out
{
    float4 _Ret [[color(0)]];
};

struct main0_in
{
    float4 IN_TexCoord [[user(locn0)]];
    float4 IN_WorldPosition [[user(locn1)]];
    half4 IN_WorldNormal [[user(locn2)]];
    half4 IN_WorldTangent [[user(locn3)]];
    half4 IN_WorldBinormal [[user(locn4)]];
    half4 IN_TintColor [[user(locn5)]];
    float IN_LinearZ [[user(locn6)]];
    half3 IN_LocalPosition [[user(locn7)]];
    half4 IN_StaticWorldNormal [[user(locn8)]];
};

fragment main0_out main0(main0_in in [[stage_in]], constant _Block1T& _Block1 [[buffer(0)]], texture2d<half> sBaseSampler [[texture(0)]], texture2d<half> sMixSampler [[texture(1)]], texture2d<half> sNormalSampler [[texture(2)]], texture2d<half> sEmissionMapSampler [[texture(3)]], texture2d<float> sNoiseSampler [[texture(4)]], texture2d<float> sCharInteractionSampler [[texture(5)]], texture2d_array<float> sSHAORGBVTSampler [[texture(6)]], texture2d_array<float> sSHAOAlphaVTSampler [[texture(7)]], depth2d_array<float> sShadowMapArraySampler [[texture(8)]], sampler sBaseSamplerSmplr [[sampler(0)]], sampler sMixSamplerSmplr [[sampler(1)]], sampler sNormalSamplerSmplr [[sampler(2)]], sampler sEmissionMapSamplerSmplr [[sampler(3)]], sampler sNoiseSamplerSmplr [[sampler(4)]], sampler sCharInteractionSamplerSmplr [[sampler(5)]], sampler sSHAORGBVTSamplerSmplr [[sampler(6)]], sampler sSHAOAlphaVTSamplerSmplr [[sampler(7)]], bool gl_FrontFacing [[front_facing]])
{
    constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::greater);
    main0_out out = {};
    half _9176 = half(0);
    half3 _9179 = half3(_9176);
    half _9199 = half(1);
    float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);
    float3 _8925 = float3(in.IN_WorldNormal.xyz);
    half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));
    half4 _8939 = sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy);
    half3 _8941 = _8939.xyz;
    half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor));
    float4 _8973 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale));
    half4 _8974 = half4(_8973);
    float4 _8982 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale));
    half4 _8983 = half4(_8982);
    float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Block1.cNoise2Bias), 0.0, 1.0);
    half _8994 = _8974.x;
    half _8996 = _8983.x;
    float _9014 = 1.0 - _8991;
    half _9019 = half(1.0);
    if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05))), 3.0)))) - 0.100000001490116119384765625) < 0.0)
    {
        discard_fragment();
    }
    half4 _9248 = sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy);
    half _9251 = half(2);
    half3 _9254 = half3(_9199);
    half3 _9255 = (_9248.xyz * _9251) - _9254;
    half _9257 = _9255.x;
    half _9263 = _9255.y;
    half _9270 = _9255.z;
    float3 _9279 = float3(((in.IN_WorldTangent.xyz * _9257) + (in.IN_WorldBinormal.xyz * _9263)) + (in.IN_WorldNormal.xyz * _9270));
    float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);
    float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
    half _9334 = half((_9331 * rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e-06))).y);
    half3 _9064 = mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279), 9.9999997473787516355514526367188e-06))), half3(half(_Block1.cNormalMapStrength)));
    half4 _9074 = sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy);
    half _9079 = _9074.y;
    half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));
    float _9100 = float(_9096);
    float3 _9109 = float3(_9064);
    half4 _9130 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy);
    half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));
    half _18217;
    if (!gl_FrontFacing)
    {
        _18217 = _9334 * half(-1);
    }
    else
    {
        _18217 = _9334;
    }
    float3 _9698 = float3(_9179);
    float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x) * _Block1.cCIFadeTime.z, 0.0, 1.0));
    float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0);
    float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0) * 0.699999988079071044921875, 0.0, 1.0)));
    half3 _9761 = mix(_8949, half3(half(0.20700000226497650146484375), half(0.18400000035762786865234375), half(0.1369999945163726806640625)), half3(half(_9734 * _9408)));
    half _9772 = half(mix(float(_9199 - _9074.x), 0.89999997615814208984375, _9734 * float(half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x), _9408, _Block1.cCIFadeTime.y)))));
    float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0);
    float4 _9443 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _Block1.cCISnowData.x) * 12.0));
    half3 _18225;
    half _18234;
    half _18236;
    half3 _18268;
    half _18272;
    if (_Block1.cCISwitchData.x > 0.0)
    {
        float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));
        float _9462 = fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0) + _9460);
        float _9499 = 1.0 - _9429;
        float _9505 = _9443.y;
        float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));
        float _9519 = float(half(9.9956989288330078125e-05));
        half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));
        float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));
        float _9557 = 1.0 - _9556;
        half _9585 = half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)) * float(max(half(fast::clamp((fast::max(_9505, _9443.w) - _9510) / fast::max(_9519, fast::clamp(_9510 + 0.1500000059604644775390625, 0.0, 1.0) - _9510), 0.0, 1.0)), half(fast::clamp((fast::max(_9505, _9443.z) - _9557) / fast::max(_9519, (1.5 - _9556) - _9557), 0.0, 1.0)))));
        half _9588 = _9199 - _9585;
        float _9603 = float(_9585);
        _18272 = half(mix(float(_9772), 1.0, _9603));
        _18268 = _9145 * _9588;
        _18236 = half(mix(_9100, 1.0, _9603));
        _18234 = _9079 * _9588;
        _18225 = mix(_9761, half3(half(0.61000001430511474609375), half(0.660000026226043701171875), half(0.790000021457672119140625)), half3(_9585));
    }
    else
    {
        _18272 = _9772;
        _18268 = _9145;
        _18236 = _9096;
        _18234 = _9079;
        _18225 = _9761;
    }
    half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));
    float _8298 = float(_8939.w * half((_8991 * float(min(_8994, _8996))) * powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05))), _Block1.cFurFadeInt)));
    half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875));
    half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));
    half _9787 = half(_8298);
    half _18230;
    half3 _18255;
    if (_Block1.eDynamicFresnelIntensity > 0.0)
    {
        float _9806 = abs(dot(_9109, -_8921));
        float _9813 = abs(_Block1.eFresnelPower);
        float _9831 = fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0) ? powr(_9806, _9813) : powr(1.0 - fast::min(float(_9199 - half(9.9956989288330078125e-05)), _9806), _9813));
        float _9846 = float(_9787 * half(_9831));
        _18255 = _9179 + half3((((float3(_Block1.eFresnelColor) * _9831) * _Block1.eFresnelIntensity) * 1.0) * _9846);
        _18230 = clamp(half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0) * _8298, _9846)), half(0.0), half(1.0));
    }
    else
    {
        _18255 = _9179;
        _18230 = _9787;
    }
    float _9868 = float(_18230);
    half _8346 = _18236 * half(_Block1.SHAOParam.w);
    float4 _9926 = float4((in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow)) - _Block1.CameraPos.xyz, 1.0) * _Block1.ShadowViewProjTexs0;
    float4 _17899 = _9926;
    _17899.z = _9926.z - _Block1.CSMShadowBiases.x;
    float4 _9942 = float4(in.IN_WorldPosition.xyz, 1.0);
    float4 _9945 = _9942 * _Block1.ShadowViewProjTexs1;
    float4 _17902 = _9945;
    _17902.z = _9945.z - _Block1.CSMShadowBiases.y;
    float4 _18237;
    if (_Block1.CSMCacheIndexs.z > 0.0)
    {
        float4 _9971 = _9942 * _Block1.ShadowViewProjTexs2;
        _9971.z = _9971.z - _Block1.CSMShadowBiases.z;
        _18237 = _9971;
    }
    else
    {
        _18237 = float4(0.0, 0.0, 0.0, 1.0);
    }
    float3 _10033 = _17902.xyz / float3(_9945.w);
    float3 _10040 = _18237.xyz / float3(_18237.w);
    float3 _10077 = _10040 * (step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.z) * float(all(_10040 > float3(0.0)) && all(_10040 < float3(1.0))));
    float _21135 = step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.y) * float(all(_10033 > float3(0.0)) && all(_10033 < float3(1.0)));
    float3 _21138 = _10077 + ((_10033 - _10077) * _21135);
    float _10113 = _21138.z;
    float2 _10120 = float2(_Block1.cShadowBias.w);
    float2 _10167 = (_21138.xy / _10120) - float2(0.5);
    float2 _10169 = fract(_10167);
    float2 _10171 = floor(_10167);
    float2 _10177 = float2(2.0) - _10169;
    float2 _10181 = _10169 + float2(1.0);
    float2 _10184 = float2(1.0) / _10177;
    float2 _10187 = _10169 / _10181;
    float _10205 = float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]));
    float3 _10208 = float3(((_10171 + float2(-0.5)) + _10184) * _10120, _10205);
    float3 _10231 = float3(((_10171 + float2(1.5, -0.5)) + float2(_10187.x, _10184.y)) * _10120, _10205);
    float3 _10254 = float3(((_10171 + float2(-0.5, 1.5)) + float2(_10184.x, _10187.y)) * _10120, _10205);
    float3 _10276 = float3(((_10171 + float2(1.5)) + _10187) * _10120, _10205);
    float _10282 = _10177.x;
    float _10289 = _10181.x;
    float _10300 = _10181.y;
    float3 _9997 = _17899.xyz / float3(_9926.w);
    float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);
    float3 _17928 = _9997;
    _17928.z = _10004;
    float2 _10378 = (_17928.xy / _10120) - float2(0.5);
    float2 _10380 = fract(_10378);
    float2 _10382 = floor(_10378);
    float2 _10388 = float2(2.0) - _10380;
    float2 _10392 = _10380 + float2(1.0);
    float2 _10395 = float2(1.0) / _10388;
    float2 _10398 = _10380 / _10392;
    float _10416 = float(int(_Block1.CSMCacheIndexs.x));
    float3 _10419 = float3(((_10382 + float2(-0.5)) + _10395) * _10120, _10416);
    float3 _10442 = float3(((_10382 + float2(1.5, -0.5)) + float2(_10398.x, _10395.y)) * _10120, _10416);
    float3 _10465 = float3(((_10382 + float2(-0.5, 1.5)) + float2(_10395.x, _10398.y)) * _10120, _10416);
    float3 _10487 = float3(((_10382 + float2(1.5)) + _10398) * _10120, _10416);
    float _10493 = _10388.x;
    float _10500 = _10392.x;
    float _10511 = _10392.y;
    half _8351 = max(half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)) + ((2.0 * _9100) * _9100)) - 1.0, 0.0, 1.0), _Block1.cMicroShadow)), max(half(((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)), _10004, level(0.0)) * _10493) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10442.xy, uint(rint(_10442.z)), _10004, level(0.0)) * _10500))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10465.xy, uint(rint(_10465.z)), _10004, level(0.0)) * _10493) * _10511)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10487.xy, uint(rint(_10487.z)), _10004, level(0.0)) * _10500) * _10511)) * 0.111111097037792205810546875) * float(all(_17928 > float3(0.0)) && all(_17928 < float3(1.0)))), half(fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)), _10113, level(0.0)) * _10282) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10231.xy, uint(rint(_10231.z)), _10113, level(0.0)) * _10289))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10254.xy, uint(rint(_10254.z)), _10113, level(0.0)) * _10282) * _10300)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10276.xy, uint(rint(_10276.z)), _10113, level(0.0)) * _10289) * _10300)) * 0.111111097037792205810546875) * float(all(_21138 > float3(0.0)) && all(_21138 < float3(1.0)))))))));
    float3 _8370 = in.IN_WorldPosition.xyz - _Block1.CameraPos.xyz;
    float3 _8373 = fast::normalize(-_8370);
    float _8378 = dot(_9109, _8373);
    half3 _10557 = mix(half3(half(0.039999999105930328369140625)), _8315, half3(_18234));
    half3 _10569 = half3(float3(_8315 - (_8315 * _18234)) * float3(0.3183098733425140380859375));
    float3 _10588 = float3(_Block1.EnvInfo.z);
    half3 _8393 = half3(half(0.0));
    uint _8397 = as_type<uint>(_Block1.SHGIParam.w);
    bool _8401 = (_8397 & 63u) > 0u;
    half _18329;
    if (_8401)
    {
        float3 _8435 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u));
        half _18306;
        if (_8401)
        {
            half _18304;
            if ((_8397 & 8u) != 0u)
            {
                float3 _10686 = (in.IN_WorldPosition.xyz + (_8373 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);
                float3 _10762 = _10686 - floor(_10686);
                float3 _10789 = _8435 * float3(0.125);
                float _10797 = _10789.x;
                float _10799 = floor(_10797);
                float3 _21191;
                _21191.x = _10799 - 15.0;
                float3 _21235;
                if ((_10797 - _10799) > 0.5)
                {
                    float3 _21194 = _21191;
                    _21194.x = _10799 + (-14.0);
                    _21235 = _21194;
                }
                else
                {
                    _21235 = _21191;
                }
                float _21078 = _10789.y;
                float _21079 = floor(_21078);
                float3 _21198 = _21235;
                _21198.y = _21079 - 8.0;
                float3 _21236;
                if ((_21078 - _21079) > 0.5)
                {
                    float3 _21201 = _21198;
                    _21201.y = _21079 + (-7.0);
                    _21236 = _21201;
                }
                else
                {
                    _21236 = _21198;
                }
                float _21100 = _10789.z;
                float _21101 = floor(_21100);
                float3 _21205 = _21236;
                _21205.z = _21101 - 15.0;
                float3 _21237;
                if ((_21100 - _21101) > 0.5)
                {
                    float3 _21208 = _21205;
                    _21208.z = _21101 + (-14.0);
                    _21237 = _21208;
                }
                else
                {
                    _21237 = _21205;
                }
                float3 _10822 = _21237 * 8.0;
                half _18305;
                if (all(in.IN_WorldPosition.xyz >= _10822) && all(in.IN_WorldPosition.xyz < (_10822 + float3(240.0, 128.0, 240.0))))
                {
                    uint _10704 = (_8397 & 251658240u) >> 24u;
                    float _10887 = 3.0 - float((_8397 & 458752u) >> 16u);
                    half _18297;
                    if (_10704 <= 3u)
                    {
                        float _10900 = 3.0 - float(_10704);
                        float2 _10994 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
                        float _11001 = _10822.x * 0.0041666668839752674102783203125;
                        float _11005 = ((_11001 - floor(_11001)) - 0.5) * 0.9375;
                        float _11011 = _10822.z * 0.0041666668839752674102783203125;
                        float _11015 = ((_11011 - floor(_11011)) - 0.5) * 0.9375;
                        float _11020 = _10994.x;
                        float3 _17954;
                        _17954.x = (_11020 < (_11005 + 0.5)) ? fast::min(_11020, _11005 + 0.49609375) : fast::max(_11020, _11005 + 0.50390625);
                        float _11038 = _10994.y;
                        _17954.z = (_11038 < (_11015 + 0.5)) ? fast::min(_11038, _11015 + 0.49609375) : fast::max(_11038, _11015 + 0.50390625);
                        float _11059 = (_10762.y * 64.0) - 0.5;
                        float _11064 = floor(_11059);
                        uint _11067 = (_11059 < 0.0) ? 63u : uint(_11064);
                        uint _11070 = _11067 + 1u;
                        uint _21301 = (_11070 >= 64u) ? 0u : _11070;
                        float2 _11097 = (float2(float(_11067 & 7u), float(_11067 >> 3u)) + _17954.xz) * 0.125;
                        float _11100 = _11097.x;
                        float3 _11102 = float3(_11100, _11097.y, _10887);
                        half4 _17962;
                        _17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)), level(0.0)).x);
                        float3 _11113 = float3(_11100, _11097.y, _10900);
                        half3 _11118 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)), level(0.0)).xyz);
                        float2 _11135 = (float2(float(_21301 & 7u), float(_21301 >> 3u)) + _17954.xz) * 0.125;
                        float _11138 = _11135.x;
                        float3 _11140 = float3(_11138, _11135.y, _10887);
                        half4 _17964;
                        _17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)), level(0.0)).x);
                        float3 _11151 = float3(_11138, _11135.y, _10900);
                        half3 _11156 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)), level(0.0)).xyz);
                        half4 _11163 = mix(half4(_11118.x, _11118.y, _11118.z, _17962.w), half4(_11156.x, _11156.y, _11156.z, _17964.w), half4(half(fast::clamp(_11059 - _11064, 0.0, 1.0))));
                        _18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * float3(2.0)) - float3(1.0)))) * 2.0), half(0.0), half(1.0));
                    }
                    else
                    {
                        float2 _11233 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
                        float _11240 = _10822.x * 0.0041666668839752674102783203125;
                        float _11244 = ((_11240 - floor(_11240)) - 0.5) * 0.9375;
                        float _11250 = _10822.z * 0.0041666668839752674102783203125;
                        float _11254 = ((_11250 - floor(_11250)) - 0.5) * 0.9375;
                        float _11259 = _11233.x;
                        float3 _17977;
                        _17977.x = (_11259 < (_11244 + 0.5)) ? fast::min(_11259, _11244 + 0.49609375) : fast::max(_11259, _11244 + 0.50390625);
                        float _11277 = _11233.y;
                        _17977.z = (_11277 < (_11254 + 0.5)) ? fast::min(_11277, _11254 + 0.49609375) : fast::max(_11277, _11254 + 0.50390625);
                        float _11298 = (_10762.y * 64.0) - 0.5;
                        float _11303 = floor(_11298);
                        uint _11306 = (_11298 < 0.0) ? 63u : uint(_11303);
                        uint _11309 = _11306 + 1u;
                        uint _21300 = (_11309 >= 64u) ? 0u : _11309;
                        float3 _11340 = float3((float2(float(_11306 & 7u), float(_11306 >> 3u)) + _17977.xz) * 0.125, _10887);
                        float3 _11365 = float3((float2(float(_21300 & 7u), float(_21300 >> 3u)) + _17977.xz) * 0.125, _10887);
                        _18297 = half(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)), level(0.0)).x)), float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)), level(0.0)).x)), fast::clamp(_11298 - _11303, 0.0, 1.0))) * half(32.0);
                    }
                    float3 _11404 = (((in.IN_WorldPosition.xyz - _10822) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);
                    float3 _11407 = _11404 * _11404;
                    float3 _11410 = _11407 * _11407;
                    half _18303;
                    if ((!((_8397 & 4u) != 0u)) && ((_8397 & 32768u) > 0u))
                    {
                        _18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0)));
                    }
                    else
                    {
                        _18303 = _18297;
                    }
                    _18305 = _18303;
                }
                else
                {
                    _18305 = _9019;
                }
                _18304 = _18305;
            }
            else
            {
                _18304 = _9019;
            }
            float _11467 = _Block1.SHAOParam.z * _Block1.SHAOParam.z;
            float3 _11470 = in.IN_WorldPosition.xyz - _8435;
            float _11479 = fast::clamp((_11467 - dot(_11470, _11470)) / _11467, 0.0, 1.0);
            _18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0)))) * float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)))), 0.0, 1.0));
        }
        else
        {
            _18306 = _9019;
        }
        half _18330;
        if (!((_8397 & 64u) > 0u))
        {
            _18330 = _8346 * _18306;
        }
        else
        {
            _18330 = _8346;
        }
        _18329 = _18330;
    }
    else
    {
        _18329 = _8346;
    }
    float3 _11517 = float3(half3(_8373));
    float3 _11600 = _Block1.CameraPos.xyz + (fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0), _Block1.SunDirection.z)) * 200000.0);
    float3 _11604 = reflect(-_11517, _9109);
    float3 _11611 = (_11604 * dot(_11600, _11604)) - _11600;
    float3 _11622 = fast::normalize(_11600 + (_11611 * fast::clamp(4500.0 / length(_11611), 0.0, 1.0)));
    half _11536 = clamp(half(dot(_9109, _11622)), half(0.0), half(1.0));
    float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));
    float _11629 = fast::max(0.00999999977648258209228515625, fast::clamp((4.125 * _11560) - 0.319999992847442626953125, 0.0, 1.0));
    float _11919 = float(_11536);
    float _11925 = dot(_9109, _11517);
    float3 _11940 = fast::normalize(_11517 + _11622);
    float _11945 = fast::clamp(dot(_9109, _11940), 0.0, 1.0);
    float _11951 = fast::clamp(dot(_11517, _11940), 0.0, 1.0);
    float _11736 = fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);
    half4 _11959 = half4(half(0.60000002384185791015625));
    half4 _11967 = _11959 * _11959;
    half4 _11970 = half4(half(1.0)) - _11967;
    half4 _11973 = half4(half(1.0)) + _11967;
    half4 _11975 = _11959 * half(2.0);
    half4 _11982 = half4(half(1.5));
    float _11756 = exp2((((-5.554729938507080078125) * _11951) - 6.9831600189208984375) * _11951);
    float _11763 = _11756 + ((1.0 - _11756) * 0.039999999105930328369140625);
    half _11764 = half(0.699999988079071044921875);
    half _11768 = half(float(_11764) + 0.100000001490116119384765625);
    half _11772 = _9199 - _8351;
    half _11777 = _9199 + _11768;
    half _11790 = _9199 + _11764;
    half3 _11812 = half3((float3(half3(_Block1.SunColor.xyz)) * _11919) * float(clamp(((_11772 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_11536 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
    half _11815 = half(10.0);
    half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));
    float _11858 = float(_11815) * 0.5;
    float _11862 = float(_9251 + _11815);
    float _12049 = _11560 * _11560;
    float _12062 = _12049 / (((((_11945 * _12049) * _12049) - _11945) * _11945) + 1.0);
    float _12080 = _12049 * _12049;
    float _12108 = float(half(9.9956989288330078125e-05));
    float3 _12025 = float3(_10557);
    float3 _12120 = float3(fast::clamp(50.0 * _12025.y, 0.0, 1.0)) - _12025;
    float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_11517, _11622))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763 * _11763) * float(dot(_11812, _8303)), 0.0, 1.0)))) * _11812) * float3(_11629))) + (((float3(_11812) * ((_12025 + (_12120 * _11756)) * (fast::min(1000.0, (_12062 * _12062) * 0.3183098733425140380859375) * (1.0 / fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080))) + _12080)) * (_11919 + sqrt((_11919 * (_11919 - (_11919 * _12080))) + _12080)), _12108))))) * _11629) * float(_11772))) * _10588;
    uint _12171 = uint(_Block1.LightDataBuffer[0].x);
    half3 _18429;
    float3 _18431;
    float3 _19685;
    float3 _19720;
    float3 _19755;
    half3 _19825;
    _19825 = _18526;
    _19755 = _19185;
    _19720 = _19185;
    _19685 = _19185;
    _18431 = _9698;
    _18429 = _9179;
    half3 _19923;
    float3 _19965;
    half _20833;
    float _20848;
    int _20863;
    float3 _20893;
    float3 _20908;
    float3 _20923;
    float _20938;
    half3 _20953;
    half _19485;
    float _19577;
    int _19613;
    float _19790;
    for (uint _18428 = 0u; _18428 < _12171; _19825 = _20953, _19790 = _20938, _19755 = _20923, _19720 = _20908, _19685 = _20893, _19613 = _20863, _19577 = _20848, _19485 = _20833, _18431 = _19965, _18429 = _19923, _18428++)
    {
        uint _12181 = _18428 * 4u;
        int _12188 = int(_12181 + 1u);
        int _12195 = int(_12181 + 2u);
        int _12202 = int(_12181 + 3u);
        int _12209 = int(_12181 + 4u);
        uint _12298 = as_type<uint>(_Block1.LightDataBuffer[_12209].x);
        if (!((_12298 & 2097152u) == 2097152u))
        {
            _20953 = _19825;
            _20938 = _19790;
            _20923 = _19755;
            _20908 = _19720;
            _20893 = _19685;
            _20863 = _19613;
            _20848 = _19577;
            _20833 = _19485;
            _19965 = _18431;
            _19923 = _18429;
            continue;
        }
        uint _12309 = _12298 & 196608u;
        half _19481;
        float _19573;
        int _19609;
        float3 _19681;
        float3 _19716;
        float3 _19751;
        float _19786;
        half3 _19821;
        if (_12309 == 196608u)
        {
            float3 _12360 = -_Block1.LightDataBuffer[_12202].xyz;
            float3 _12378 = in.IN_WorldPosition.xyz - (_Block1.LightDataBuffer[_12188].xyz + (_12360 * (dot(in.IN_WorldPosition.xyz - _Block1.LightDataBuffer[_12188].xyz, _12360) / dot(_12360, _12360))));
            float _12381 = dot(_12378, _12378);
            float _19350;
            if (_12381 > (_Block1.LightDataBuffer[_12209].y * _Block1.LightDataBuffer[_12209].y))
            {
                float _12392 = sqrt(_12381) - _Block1.LightDataBuffer[_12209].y;
                float _12395 = _12392 * _12392;
                float _12398 = _12395 * abs(_Block1.LightDataBuffer[_12188].w);
                float _12404 = fast::clamp(1.0 - (_12398 * _12398), 0.0, 1.0);
                _19350 = fast::min(100.0, (_12404 * _12404) / (_12395 + 1.0));
            }
            else
            {
                _19350 = 1.0;
            }
            _19821 = half3(_Block1.LightDataBuffer[_12195].xyz);
            _19786 = _19350;
            _19751 = _12360;
            _19716 = _11517;
            _19681 = _9109;
            _19609 = 0;
            _19573 = abs(_Block1.LightDataBuffer[_12195].w);
            _19481 = clamp(half(dot(_9109, _12360)), half(0.0), half(1.0));
        }
        else
        {
            half _19482;
            float _19574;
            int _19610;
            float3 _19682;
            float3 _19717;
            float3 _19752;
            float _19787;
            half3 _19822;
            if (_12309 == 0u)
            {
                uint _12741 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
                float _12858 = float((_12741 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
                float3 _12599 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
                float _12602 = dot(_12599, _12599);
                float3 _12606 = _12599 * rsqrt(_12602);
                float _12613 = _12602 * abs(_Block1.LightDataBuffer[_12188].w);
                float _12620 = fast::clamp(1.0 - (_12613 * _12613), 0.0, 1.0);
                float _12631 = _12620 * _12620;
                float _19213;
                if ((_12298 & 16777216u) == 16777216u)
                {
                    float _12641 = _12631 / ((_12602 * _Block1.LightDataBuffer[_12202].w) + 9.9999997473787516355514526367188e-05);
                    float _19211;
                    if (_12858 > 0.00999999977648258209228515625)
                    {
                        _19211 = fast::min(_12641, _12858);
                    }
                    else
                    {
                        _19211 = _12641;
                    }
                    _19213 = fast::min(100.0, _19211);
                }
                else
                {
                    _19213 = _12631 * 0.100000001490116119384765625;
                }
                float _12677 = fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606) - _Block1.LightDataBuffer[_12209].z) * _Block1.LightDataBuffer[_12209].y, 0.0, 1.0);
                _19822 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19213));
                _19787 = _12677 * _12677;
                _19752 = _12606;
                _19717 = _11517;
                _19682 = _9109;
                _19610 = 0;
                _19574 = float((_12741 >> 16u) & 65535u) * 0.001525902189314365386962890625;
                _19482 = clamp(half(dot(_9109, _12606)), half(0.0), half(1.0));
            }
            else
            {
                half _19483;
                float _19575;
                int _19611;
                float3 _19683;
                float3 _19718;
                float3 _19753;
                float _19788;
                half3 _19823;
                if (_12309 == 65536u)
                {
                    uint _13098 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
                    float3 _12933 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
                    float _12936 = dot(_12933, _12933);
                    float3 _12942 = _12933 / float3(sqrt(_12936));
                    float _12950 = _12936 * abs(_Block1.LightDataBuffer[_12188].w);
                    float _12956 = fast::clamp(1.0 - (_12950 * _12950), 0.0, 1.0);
                    float _12972 = fast::min(100.0, (_12956 * _12956) / ((_12936 * _Block1.LightDataBuffer[_12209].w) + 9.9999997473787516355514526367188e-05));
                    float _13202 = float((_13098 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
                    float _19070;
                    if (_13202 > 0.00999999977648258209228515625)
                    {
                        _19070 = fast::min(_12972, _13202);
                    }
                    else
                    {
                        _19070 = _12972;
                    }
                    _19823 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19070) * (((_12298 & 16777216u) == 16777216u) ? _Block1.TimeOfDayInfos.y : 1.0));
                    _19788 = 1.0;
                    _19753 = _12942;
                    _19718 = _11517;
                    _19683 = _9109;
                    _19611 = 0;
                    _19575 = float((_13098 >> 16u) & 65535u) * 0.001525902189314365386962890625;
                    _19483 = ((_12298 & 262144u) == 262144u) ? _9019 : half(fast::clamp(dot(_9109, _12942), 0.0, 1.0));
                }
                else
                {
                    bool _13270 = _12309 == 131072u;
                    half _19484;
                    float3 _19684;
                    float3 _19754;
                    float _19789;
                    half3 _19824;
                    if (_13270)
                    {
                        float3 _13339 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
                        float _13342 = dot(_13339, _13339);
                        float _13348 = _13342 * abs(_Block1.LightDataBuffer[_12188].w);
                        float _13356 = fast::clamp(1.0 - (_13348 * _13348), 0.0, 1.0);
                        float3 _13433 = fast::normalize(_11517 - (_9109 * _11925));
                        float3x3 _13459 = float3x3(_13433, cross(_9109, _13433), _9109);
                        float3 _13466 = float3(_Block1.LightDataBuffer[_12202].xyz) * _Block1.LightDataBuffer[_12195].w;
                        float3 _13467 = _13339 - _13466;
                        float3 _13472 = float3(_Block1.LightDataBuffer[_12209].yzw) * _Block1.LightDataBuffer[_12202].w;
                        float3 _13484 = _13339 + _13466;
                        float3 _13657 = fast::normalize((_13467 - _13472) * _13459);
                        float3 _13660 = fast::normalize((_13484 - _13472) * _13459);
                        float3 _13663 = fast::normalize((_13484 + _13472) * _13459);
                        float3 _13666 = fast::normalize((_13467 + _13472) * _13459);
                        float _13712 = dot(_13657, _13660);
                        float _13714 = abs(_13712);
                        float _13728 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13714)) * _13714)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13714) * _13714));
                        float _13753 = dot(_13660, _13663);
                        float _13755 = abs(_13753);
                        float _13769 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13755)) * _13755)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13755) * _13755));
                        float _13794 = dot(_13663, _13666);
                        float _13796 = abs(_13794);
                        float _13810 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13796)) * _13796)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13796) * _13796));
                        float _13835 = dot(_13666, _13657);
                        float _13837 = abs(_13835);
                        float _13851 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13837)) * _13837)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13837) * _13837));
                        float3 _13700 = cross(_13660, (_13657 * (-((_13712 > 0.0) ? _13728 : ((0.5 * rsqrt(fast::max(1.0 - (_13712 * _13712), 1.0000000116860974230803549289703e-07))) - _13728)))) + (_13663 * ((_13753 > 0.0) ? _13769 : ((0.5 * rsqrt(fast::max(1.0 - (_13753 * _13753), 1.0000000116860974230803549289703e-07))) - _13769)))) + cross(_13666, (_13657 * ((_13835 > 0.0) ? _13851 : ((0.5 * rsqrt(fast::max(1.0 - (_13835 * _13835), 1.0000000116860974230803549289703e-07))) - _13851))) + (_13663 * (-((_13794 > 0.0) ? _13810 : ((0.5 * rsqrt(fast::max(1.0 - (_13794 * _13794), 1.0000000116860974230803549289703e-07))) - _13810)))));
                        float _13531 = length(_13700);
                        float _13539 = step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_12209].yzw), _13339));
                        _19824 = half3(_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356));
                        _19789 = ((!((_12298 & 67108864u) == 67108864u)) && (_13539 > 0.0)) ? 0.0 : _13531;
                        _19754 = _13339 * rsqrt(_13342);
                        _19684 = _9109;
                        _19484 = half(fast::max(((_13531 * _13531) + ((_13700 / float3(_13531)).z * ((_13539 * 2.0) - 1.0))) / (_13531 + 1.0), 0.0));
                    }
                    else
                    {
                        _19824 = _19825;
                        _19789 = _19790;
                        _19754 = _19755;
                        _19684 = _19685;
                        _19484 = _19485;
                    }
                    _19823 = _19824;
                    _19788 = _19789;
                    _19753 = _19754;
                    _19718 = select(_19720, _11517, bool3(_13270));
                    _19683 = _19684;
                    _19611 = _13270 ? 0 : _19613;
                    _19575 = _13270 ? 1.0 : _19577;
                    _19483 = _19484;
                }
                _19822 = _19823;
                _19787 = _19788;
                _19752 = _19753;
                _19717 = _19718;
                _19682 = _19683;
                _19610 = _19611;
                _19574 = _19575;
                _19482 = _19483;
            }
            _19821 = _19822;
            _19786 = _19787;
            _19751 = _19752;
            _19716 = _19717;
            _19681 = _19682;
            _19609 = _19610;
            _19573 = _19574;
            _19481 = _19482;
        }
        float _14176 = float(_19481);
        float3 _14197 = fast::normalize(_19716 + _19751);
        float _14202 = fast::clamp(dot(_19681, _14197), 0.0, 1.0);
        float _14208 = fast::clamp(dot(_19716, _14197), 0.0, 1.0);
        float _13993 = fast::clamp(abs(fast::clamp(dot(_19681, _19716), 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);
        float _14013 = exp2((((-5.554729938507080078125) * _14208) - 6.9831600189208984375) * _14208);
        float _14020 = _14013 + ((1.0 - _14013) * 0.039999999105930328369140625);
        half _14029 = _9199 - (_9199 - _19481);
        half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_19481 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
        half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));
        float3 _19883;
        do
        {
            if (_19609 >= 1)
            {
                float _14319 = _12049 / (((((_14202 * _12049) * _12049) - _14202) * _14202) + 1.0);
                _19883 = (_12025 + (_12120 * _14013)) * (fast::min(1000.0, (_14319 * _14319) * 0.3183098733425140380859375) * (1.0 / fast::max((_13993 + sqrt((_13993 * (_13993 - (_13993 * _12080))) + _12080)) * (_14176 + sqrt((_14176 * (_14176 - (_14176 * _12080))) + _12080)), _12108)));
                break;
            }
            else
            {
                _19883 = float3(0.0);
                break;
            }
            break; // unreachable workaround
        } while(false);
        _20953 = _19821;
        _20938 = _19786;
        _20923 = _19751;
        _20908 = _19716;
        _20893 = _19681;
        _20863 = _19609;
        _20848 = _19573;
        _20833 = _19481;
        _19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_19716, _19751))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020 * _14020) * float(dot(_14069, _8303)), 0.0, 1.0)))) * _14069) * float3(_19573))) + (((float3(_14069) * _19883) * _19573) * float(_14029));
        _19923 = _18429 + (_14069 * _14029);
    }
    half3 _8560 = (_9179 + (((_9179 + (_11812 * _11772)) * _9254) * half3(half(_Block1.EnvInfo.z)))) + _18429;
    float3 _8565 = (_9698 + _8521) + _18431;
    bool _8573 = (_8397 & 16128u) > 0u;
    float3 _18989;
    half3 _19028;
    if (_8573)
    {
        bool _8590 = (_8397 & 16384u) > 0u;
        half3 _18832;
        half3 _18859;
        if (_8573)
        {
            uint _14451 = (_8397 & 458752u) >> 16u;
            uint _14469 = (_8397 & 4026531840u) >> 28u;
            float _14482 = fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ) / fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875), 0.0, 1.0);
            half3 _18824;
            half3 _18826;
            if ((_8397 & 2048u) != 0u)
            {
                float3 _14684 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u)) * float3(0.125);
                float _14692 = _14684.x;
                float _14694 = floor(_14692);
                float3 _21212;
                _21212.x = _14694 - 15.0;
                float3 _21268;
                if ((_14692 - _14694) > 0.5)
                {
                    float3 _21215 = _21212;
                    _21215.x = _14694 + (-14.0);
                    _21268 = _21215;
                }
                else
                {
                    _21268 = _21212;
                }
                float _21034 = _14684.y;
                float _21035 = floor(_21034);
                float3 _21219 = _21268;
                _21219.y = _21035 - 8.0;
                float3 _21269;
                if ((_21034 - _21035) > 0.5)
                {
                    float3 _21222 = _21219;
                    _21222.y = _21035 + (-7.0);
                    _21269 = _21222;
                }
                else
                {
                    _21269 = _21219;
                }
                float _21056 = _14684.z;
                float _21057 = floor(_21056);
                float3 _21226 = _21269;
                _21226.z = _21057 - 15.0;
                float3 _21270;
                if ((_21056 - _21057) > 0.5)
                {
                    float3 _21229 = _21226;
                    _21229.z = _21057 + (-14.0);
                    _21270 = _21229;
                }
                else
                {
                    _21270 = _21226;
                }
                float3 _14717 = _21270 * 8.0;
                half3 _18825;
                half3 _18827;
                if (all(in.IN_WorldPosition.xyz >= _14717) && all(in.IN_WorldPosition.xyz < (_14717 + float3(240.0, 128.0, 240.0))))
                {
                    float3 _14534 = (in.IN_WorldPosition.xyz + (_9109 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);
                    float3 _14747 = _14534 - floor(_14534);
                    half3 _14556 = half3(_9109);
                    float _14788 = float((_8397 & 15728640u) >> 20u);
                    float _14797 = float(_14451);
                    float _14827 = (_14788 - _14797) + ((3.0 - _14797) * 3.0);
                    float _14831 = _14827 + 1.0;
                    float _14833 = _14827 + 2.0;
                    half3 _18806;
                    half3 _18818;
                    if (3 >= int(_14469))
                    {
                        float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);
                        float _14854 = _14850 + 1.0;
                        float _14856 = _14850 + 2.0;
                        float2 _14956 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
                        float _14963 = _14717.x * 0.0041666668839752674102783203125;
                        float _14967 = ((_14963 - floor(_14963)) - 0.5) * 0.9375;
                        float _14973 = _14717.z * 0.0041666668839752674102783203125;
                        float _14977 = ((_14973 - floor(_14973)) - 0.5) * 0.9375;
                        float _14982 = _14956.x;
                        float3 _18095;
                        _18095.x = (_14982 < (_14967 + 0.5)) ? fast::min(_14982, _14967 + 0.49609375) : fast::max(_14982, _14967 + 0.50390625);
                        float _15000 = _14956.y;
                        _18095.z = (_15000 < (_14977 + 0.5)) ? fast::min(_15000, _14977 + 0.49609375) : fast::max(_15000, _14977 + 0.50390625);
                        float _15021 = (_14747.y * 64.0) - 0.5;
                        float _15026 = floor(_15021);
                        uint _15029 = (_15021 < 0.0) ? 63u : uint(_15026);
                        uint _15032 = _15029 + 1u;
                        uint _21309 = (_15032 >= 64u) ? 0u : _15032;
                        float2 _15059 = (float2(float(_15029 & 7u), float(_15029 >> 3u)) + _18095.xz) * 0.125;
                        float2 _15074 = (float2(float(_21309 & 7u), float(_21309 >> 3u)) + _18095.xz) * 0.125;
                        float _15078 = _15059.x;
                        float3 _15080 = float3(_15078, _15059.y, _14827);
                        half4 _18103;
                        _18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)), level(0.0)).x);
                        float3 _15092 = float3(_15078, _15059.y, _14850);
                        half3 _15097 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)), level(0.0)).xyz);
                        float _15104 = _15074.x;
                        float3 _15106 = float3(_15104, _15074.y, _14827);
                        half4 _18105;
                        _18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)), level(0.0)).x);
                        float3 _15118 = float3(_15104, _15074.y, _14850);
                        half3 _15123 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)), level(0.0)).xyz);
                        half4 _15132 = half4(half(fast::clamp(_15021 - _15026, 0.0, 1.0)));
                        half4 _15133 = mix(half4(_15097.x, _15097.y, _15097.z, _18103.w), half4(_15123.x, _15123.y, _15123.z, _18105.w), _15132);
                        float3 _15140 = float3(_15078, _15059.y, _14831);
                        half4 _18107;
                        _18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)), level(0.0)).x);
                        float3 _15152 = float3(_15078, _15059.y, _14854);
                        half3 _15157 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)), level(0.0)).xyz);
                        float3 _15166 = float3(_15104, _15074.y, _14831);
                        half4 _18109;
                        _18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)), level(0.0)).x);
                        float3 _15178 = float3(_15104, _15074.y, _14854);
                        half3 _15183 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)), level(0.0)).xyz);
                        half4 _15193 = mix(half4(_15157.x, _15157.y, _15157.z, _18107.w), half4(_15183.x, _15183.y, _15183.z, _18109.w), _15132);
                        float3 _15200 = float3(_15078, _15059.y, _14833);
                        half4 _18111;
                        _18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)), level(0.0)).x);
                        float3 _15212 = float3(_15078, _15059.y, _14856);
                        half3 _15217 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)), level(0.0)).xyz);
                        float3 _15226 = float3(_15104, _15074.y, _14833);
                        half4 _18113;
                        _18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)), level(0.0)).x);
                        float3 _15238 = float3(_15104, _15074.y, _14856);
                        half3 _15243 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)), level(0.0)).xyz);
                        half4 _15253 = mix(half4(_15217.x, _15217.y, _15217.z, _18111.w), half4(_15243.x, _15243.y, _15243.z, _18113.w), _15132);
                        half _15255 = half(32.0);
                        half _15258 = _15133.w * _15255;
                        half _15262 = _15193.w * _15255;
                        half _15266 = _15253.w * _15255;
                        half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;
                        half3 _18130;
                        _18130.x = half(float(dot(_14556, _15343)) * 2.0);
                        half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;
                        _18130.y = half(float(dot(_14556, _15352)) * 2.0);
                        half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;
                        _18130.z = half(float(dot(_14556, _15361)) * 2.0);
                        half3 _18819;
                        if (_8590)
                        {
                            _18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));
                        }
                        else
                        {
                            _18819 = _8393;
                        }
                        _18818 = _18819;
                        _18806 = max(half3(_15258, _15262, _15266) + (_18130 * half(mix(_Block1.SHGIParam2.z, 1.0, _14482))), _8393);
                    }
                    else
                    {
                        float2 _15427 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
                        float _15434 = _14717.x * 0.0041666668839752674102783203125;
                        float _15438 = ((_15434 - floor(_15434)) - 0.5) * 0.9375;
                        float _15444 = _14717.z * 0.0041666668839752674102783203125;
                        float _15448 = ((_15444 - floor(_15444)) - 0.5) * 0.9375;
                        float _15453 = _15427.x;
                        float3 _18143;
                        _18143.x = (_15453 < (_15438 + 0.5)) ? fast::min(_15453, _15438 + 0.49609375) : fast::max(_15453, _15438 + 0.50390625);
                        float _15471 = _15427.y;
                        _18143.z = (_15471 < (_15448 + 0.5)) ? fast::min(_15471, _15448 + 0.49609375) : fast::max(_15471, _15448 + 0.50390625);
                        float _15492 = (_14747.y * 64.0) - 0.5;
                        float _15497 = floor(_15492);
                        uint _15500 = (_15492 < 0.0) ? 63u : uint(_15497);
                        uint _15503 = _15500 + 1u;
                        uint _21308 = (_15503 >= 64u) ? 0u : _15503;
                        float2 _15530 = (float2(float(_15500 & 7u), float(_15500 >> 3u)) + _18143.xz) * 0.125;
                        float2 _15545 = (float2(float(_21308 & 7u), float(_21308 >> 3u)) + _18143.xz) * 0.125;
                        float _15549 = _15530.x;
                        float3 _15551 = float3(_15549, _15530.y, _14827);
                        half3 _18151;
                        _18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)), level(0.0)).x);
                        float _15561 = _15545.x;
                        float3 _15563 = float3(_15561, _15545.y, _14827);
                        half3 _18153;
                        _18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)), level(0.0)).x);
                        float3 _15575 = float3(_15549, _15530.y, _14831);
                        _18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)), level(0.0)).x);
                        float3 _15587 = float3(_15561, _15545.y, _14831);
                        _18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)), level(0.0)).x);
                        float3 _15599 = float3(_15549, _15530.y, _14833);
                        _18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)), level(0.0)).x);
                        float3 _15611 = float3(_15561, _15545.y, _14833);
                        _18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)), level(0.0)).x);
                        half3 _15622 = mix(_18151, _18153, half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0))));
                        half _15623 = half(32.0);
                        half3 _18164;
                        _18164.x = _15622.x * _15623;
                        _18164.y = _15622.y * _15623;
                        _18164.z = _15622.z * _15623;
                        _18818 = _8393;
                        _18806 = _18164;
                    }
                    float3 _15658 = (((in.IN_WorldPosition.xyz - _14717) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);
                    float3 _15661 = _15658 * _15658;
                    float3 _15664 = _15661 * _15661;
                    half3 _18822;
                    half3 _18823;
                    if ((max(int(_14451), 2) == 3) && ((_8397 & 32768u) > 0u))
                    {
                        half3 _14921 = half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0, 1.0)));
                        _18823 = _18818 * _14921;
                        _18822 = _18806 * _14921;
                    }
                    else
                    {
                        _18823 = _18818;
                        _18822 = _18806;
                    }
                    _18827 = _18823;
                    _18825 = _18822;
                }
                else
                {
                    _18827 = _8393;
                    _18825 = _8393;
                }
                _18826 = _18827;
                _18824 = _18825;
            }
            else
            {
                _18826 = _8393;
                _18824 = _8393;
            }
            half3 _14565 = half3(float3(0.0));
            half3 _18830;
            if (_8590)
            {
                float3 _14569 = float3(_18824);
                float _14575 = float(dot(_18826, _18826));
                half3 _18831;
                if ((float(half(((0.21267099678516387939453125 * _14569.x) + (0.71516001224517822265625 * _14569.y)) + (0.072168998420238494873046875 * _14569.z))) > 0.001000000047497451305389404296875) && (_14575 > 9.9999999747524270787835121154785e-07))
                {
                    float _14592 = fast::clamp(_9109.y, 0.0, 1.0) * 0.75;
                    float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592), 0.0, 1.0));
                    float _14604 = mix(_11560, 1.0, 0.25);
                    float _14612 = fast::clamp(dot(_9109, fast::normalize(_8373 + _14600)), 0.0, 1.0);
                    float _15697 = _14604 * _14604;
                    float _15710 = _15697 / (((((_14612 * _15697) * _15697) - _14612) * _14612) + 1.0);
                    _18831 = half3(float3(_10557 * _18824) * float3((fast::min(1000.0, (_15710 * _15710) * 0.3183098733425140380859375) * fast::clamp(dot(_9109, _14600), 0.0, 1.0)) * _Block1.SHGIParam.y));
                }
                else
                {
                    _18831 = _14565;
                }
                _18830 = _18831;
            }
            else
            {
                _18830 = _14565;
            }
            float _14641 = float(half(mix(_Block1.SHGIParam2.y, 1.0, _14482)));
            _18859 = _18830 * half3(half(_Block1.SHGIParam.y * _14641));
            _18832 = _18824 * half3(half(_Block1.SHGIParam.x * _14641));
        }
        else
        {
            _18859 = _8393;
            _18832 = _8393;
        }
        _19028 = _8560 + (_18832 * _18329);
        _18989 = _8565 + float3(_18859 * _18329);
    }
    else
    {
        _19028 = _8560;
        _18989 = _8565;
    }
    float _15792 = fast::clamp(dot(_9109, fast::normalize(_8373 + _8373)), 0.0, 1.0);
    float _15839 = _12049 / (((((_15792 * _12049) * _12049) - _15792) * _15792) + 1.0);
    float _15805 = float(half(fast::clamp(_8378, 0.0, 1.0)));
    float _15854 = _12049 * 0.5;
    float _15858 = 1.0 - _15854;
    float _15861 = (_15805 * _15858) + _15854;
    half3 _15813 = half3(_12025);
    float3 _15762 = float3(_10569);
    float3 _15920 = _Block1.cLocalVirtualLitPos.xyz + _Block1.cVirtualLitParam.xyz;
    float3 _15960 = ((((fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0))) * _15920.x) + float3(0.0, _15920.y, 0.0)) + (_8373 * _15920.z)) + (float4(0.0, 0.0, 0.0, 1.0) * _Block1.World)) - in.IN_WorldPosition.xyz;
    float3 _15970 = mix(_15960, fast::normalize(_15960), float3(step(0.0, _Block1.cLocalVirtualLitColor.w)));
    half _15974 = half(dot(_15970, _15970));
    float3 _15976 = fast::normalize(_15970);
    half _15979 = _15974 * half(1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom.x));
    float _15986 = fast::clamp(1.0 - float(_15979 * _15979), 0.0, 1.0);
    half _16031 = half((fast::clamp(dot(_9109, _15976), 0.0, 1.0) * _Block1.cLocalVirtualLitCustom.z) + (1.0 - _Block1.cLocalVirtualLitCustom.z));
    float _16112 = fast::clamp(dot(_9109, fast::normalize(_8373 + _15976)), 0.0, 1.0);
    float _16159 = _12049 / (((((_16112 * _12049) * _12049) - _16112) * _16112) + 1.0);
    float _8657 = float(_18329);
    half _8696 = half(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w), 1.0, _Block1.WorldProbeInfo.x)));
    half _16335 = half(-1.023326873779296875);
    half _16336 = half(1.023326873779296875);
    half _16342 = _9064.y;
    half _16351 = half(-0.858085691928863525390625);
    half4 _16356 = half4(_16351, half(0.7431240081787109375), _16351, half(0.4290428459644317626953125));
    half _16361 = _9064.z;
    half _16369 = _9064.x;
    half4 _16385 = _16356 * half4(_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369) - (_16342 * _16342));
    half _16387 = half(-0.2477079927921295166015625);
    _16385.y = _16385.y + _16387;
    half3 _16279 = half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125))));
    float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342))) * _9064.yzxx);
    float4 _16306 = float4(_16385);
    half3 _16397 = half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125));
    half _16509 = _16397.y;
    half _16528 = _16397.z;
    half _16536 = _16397.x;
    half4 _16552 = _16356 * half4(_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536) - (_16509 * _16509));
    _16552.y = _16552.y + _16387;
    float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509))) * _16397.yzxx);
    float4 _16473 = float4(_16552);
    half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));
    half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;
    float _16622 = length(_8370);
    float _16645 = _8370.y;
    float _16657 = (_16645 + _12108) + float((_9251 * half(9.9956989288330078125e-05)) * half(int(sign(_16645))));
    float2 _16682 = fast::max(float2(0.0), float2(_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie.y) * _Block1.CameraPos.y) * _Block1.AerialPerspectiveMie.z) * ((float2(1.0) - exp(-fast::min(float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y) * _16657, float2(10.0)))) / float2(_16657)));
    float3 _16688 = fast::max(float3(_12108), _Block1.AerialPerspectiveExt.xyz);
    float3 _16698 = float3(_8303);
    float3 _16715 = exp(-(_16688 * ((_16622 * (_Block1.FogColor.w + ((1.0 - _Block1.FogColor.w) * fast::clamp(_16622 / _Block1.FogInfo.x, 0.0, 1.0)))) * ((_16682.x / dot(_16688, _16698)) + ((_16682.y * fast::max(9.9999999747524270787835121154785e-07, _Block1.AerialPerspectiveRay.w * 0.0005000000237487256526947021484375)) * 5.0)))));
    float3 _16602 = fast::normalize(_8370);
    float _16756 = fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz), 0.0, 1.0);
    float _16759 = fast::max(0.0, _16602.y);
    float _16820 = fast::clamp((_16622 - 80.0) / fast::max(_12108, 520.0), 0.0, 1.0);
    float _16778 = 1.0 - (_16759 * _16759);
    float3 _16785 = float3(((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w)) / (12.56637096405029296875 * powr(fast::max(1.0 + (_Block1.AerialPerspectiveExt.w * (_Block1.AerialPerspectiveExt.w - (2.0 * _16756))), _12108), 1.5))) * (_16820 * _16820)) * _Block1.SunFogColor.xyz;
    half _16793 = half(_16756);
    float3 _16805 = ((_Block1.AerialPerspectiveRay.xyz * float(half(1.0) + (_16793 * _16793))) + (_16785 * _Block1.AerialPerspectiveMie.x)) + ((_Block1.FogColor.xyz * (0.0596831031143665313720703125 * (1.0 + (_16778 * _16778)))) + _16785);
    float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;
    float3 _8804 = (_16862 * _9868).xyz;
    float3 _19031;
    if (_Block1.eIsPlayerOverride < 0.5)
    {
        float3 _19032;
        if ((_Block1.ScreenMotionGray.x * _Block1.ScreenMotionGray.x) > _12108)
        {
            float _16911 = fast::clamp((_Block1.CameraPos.w - _Block1.ScreenMotionGray.w) / fast::max(_12108, (_Block1.ScreenMotionGray.w + abs(_Block1.ScreenMotionGray.x)) - _Block1.ScreenMotionGray.w), 0.0, 1.0);
            float _19029;
            if (_Block1.ScreenMotionGray.x > 0.001000000047497451305389404296875)
            {
                _19029 = 1.0 - _16911;
            }
            else
            {
                _19029 = _16911;
            }
            _19032 = mix(_8804, float3(dot(_8804, _16698) * (0.00999999977648258209228515625 * floor(_Block1.ScreenMotionGray.z))), float3(_19029 * fract(_Block1.ScreenMotionGray.z)));
        }
        else
        {
            _19032 = _8804;
        }
        _19031 = _19032;
    }
    else
    {
        _19031 = _8804;
    }
    float4 _8808 = float4(_19031.x, _19031.y, _19031.z, float4(0.0).w);
    _8808.w = _9868;
    float3 _8816 = fast::min(_8808.xyz, float3(10000.0));
    out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w);
    return out;
}

