"""
GLSL精度分析优化器 - 智能分析变量精度需求，自动选择最优的精度限定符
"""
import re
from typing import Dict, List, Tuple, Set, Optional
from .glsl_optimizer_base import GLSLOptimizerBase, OptimizationLevel

class GLSLPrecisionAnalyzer(GLSLOptimizerBase):
    """
    GLSL精度分析优化器，智能分析变量的精度需求并自动选择最优的精度限定符。
    
    支持的优化：
    - 自动精度降级（highp -> mediump -> lowp）
    - 精度升级建议（lowp -> mediump -> highp）
    - 精度传播分析
    - 数值范围分析
    - 精度损失评估
    - 移动端GPU优化
    
    用法：
        analyzer = GLSLPrecisionAnalyzer()
        optimized_code = analyzer.optimize_code(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # GLSL精度要求分类
        self.precision_requirements = {
            'high_precision': {
                'operations': ['matrix_multiply', 'vertex_transform', 'lighting_calculation'],
                'variables': ['gl_Position', 'worldMatrix', 'viewMatrix', 'projMatrix'],
                'functions': ['normalize', 'reflect', 'refract']
            },
            'medium_precision': {
                'operations': ['texture_coordinate', 'color_calculation', 'normal_calculation'],
                'variables': ['texCoord', 'normal', 'color', 'diffuse', 'specular'],
                'functions': ['dot', 'cross', 'length', 'distance']
            },
            'low_precision': {
                'operations': ['alpha_test', 'simple_blend', 'fog_calculation'],
                'variables': ['alpha', 'fog', 'blend', 'mask'],
                'functions': ['mix', 'step', 'smoothstep']
            }
        }
        
        # 精度限定符映射
        self.precision_qualifiers = ['lowp', 'mediump', 'highp']
        
        # 默认精度设置（移动端优化）
        self.default_precision = {
            'float': 'mediump',
            'int': 'mediump',
            'sampler2D': 'lowp',
            'samplerCube': 'lowp'
        }
    
    def optimize_code(self, code: str) -> str:
        """优化GLSL精度代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._analyze_variable_precision(optimized_code)
            optimized_code = self._add_default_precision(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._propagate_precision(optimized_code)
            optimized_code = self._optimize_texture_precision(optimized_code)
            optimized_code = self._downgrade_unnecessary_precision(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._analyze_precision_loss(optimized_code)
            optimized_code = self._suggest_precision_improvements(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _analyze_variable_precision(self, code: str) -> str:
        """分析变量精度需求"""
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            # 跳过precision声明，避免误处理
            if 'precision' in line:
                optimized_lines.append(line)
                continue

            # 跳过return语句、函数调用等，只处理变量声明
            if any(keyword in line for keyword in ['return', 'if', 'for', 'while', '(', ')', '=', '+', '-', '*', '/']):
                optimized_lines.append(line)
                continue

            # 查找变量声明（更严格的匹配）
            var_match = re.match(r'^\s*((?:highp|mediump|lowp)?\s*)(float|vec[234]|mat[234]|int|uint|bool|sampler\w*)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*;?\s*$', line.strip())
            if var_match:
                current_precision = var_match.group(1).strip()
                var_type = var_match.group(2)
                var_name = var_match.group(3)
                
                # 分析变量的使用模式
                suggested_precision = self._suggest_precision_for_variable(var_name, var_type, code)
                
                if not current_precision and suggested_precision:
                    # 添加精度限定符
                    new_line = line.replace(f'{var_type} {var_name}', f'{suggested_precision} {var_type} {var_name}')
                    optimized_lines.append(new_line)
                    self.statistics['optimizations_applied'] += 1
                elif current_precision and suggested_precision and current_precision != suggested_precision:
                    # 更新精度限定符
                    new_line = line.replace(current_precision, suggested_precision)
                    optimized_lines.append(new_line)
                    self.statistics['optimizations_applied'] += 1
                else:
                    optimized_lines.append(line)
            else:
                optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _add_default_precision(self, code: str) -> str:
        """添加默认精度声明"""
        lines = code.splitlines()
        
        # 检查是否已有精度声明
        has_precision_declarations = any('precision' in line and 'default' not in line for line in lines)
        
        if not has_precision_declarations:
            # 添加默认精度声明
            precision_declarations = [
                'precision mediump float;',
                'precision mediump int;',
                'precision lowp sampler2D;',
                'precision lowp samplerCube;'
            ]
            
            # 找到合适的插入位置（在#version之后，在其他代码之前）
            insert_index = 0
            for i, line in enumerate(lines):
                if line.strip().startswith('#version'):
                    insert_index = i + 1
                    break
                elif line.strip() and not line.strip().startswith('//'):
                    break
            
            # 插入精度声明
            for i, declaration in enumerate(precision_declarations):
                lines.insert(insert_index + i, declaration)
            
            self.statistics['optimizations_applied'] += len(precision_declarations)
        
        return '\n'.join(lines)
    
    def _propagate_precision(self, code: str) -> str:
        """传播精度分析"""
        # 分析变量之间的依赖关系，传播精度要求
        variables = self.extract_variables(code)
        precision_map = {}
        
        # 构建变量精度映射
        for var in variables:
            precision = self.get_precision_qualifier(var, code)
            if precision:
                precision_map[var] = precision
        
        # 分析赋值和运算，传播精度
        lines = code.splitlines()
        for line in lines:
            # 查找赋值操作
            assignment_match = re.match(r'^\s*([a-zA-Z0-9_]+)\s*=\s*([^;]+);', line.strip())
            if assignment_match:
                target_var = assignment_match.group(1)
                expression = assignment_match.group(2)
                
                # 分析表达式中的变量
                expr_vars = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', expression)
                
                # 确定所需的精度
                required_precision = self._determine_expression_precision(expr_vars, precision_map)
                if required_precision and target_var in precision_map:
                    precision_map[target_var] = max(precision_map[target_var], required_precision, 
                                                  key=lambda x: self.precision_qualifiers.index(x))
        
        return code
    
    def _optimize_texture_precision(self, code: str) -> str:
        """优化纹理采样精度"""
        optimizations = [
            # 对于简单的纹理查找，使用lowp
            (re.compile(r'(highp|mediump)\s+(texture\s*\([^)]+\))'), r'lowp \2'),
            
            # 对于纹理坐标，通常mediump就足够了
            (re.compile(r'highp\s+(vec[234])\s+(\w*[Tt]ex[Cc]oord\w*)'), r'mediump \1 \2'),
        ]
        
        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1
        
        return code
    
    def _downgrade_unnecessary_precision(self, code: str) -> str:
        """降级不必要的高精度"""
        # 查找可以安全降级的变量
        optimizations = [
            # 简单的颜色计算可以使用mediump
            (re.compile(r'highp\s+(vec[34])\s+(\w*[Cc]olor\w*)'), r'mediump \1 \2'),
            
            # alpha值通常只需要lowp
            (re.compile(r'(highp|mediump)\s+(float)\s+(\w*[Aa]lpha\w*)'), r'lowp \2 \3'),
            
            # 雾效计算通常lowp就足够
            (re.compile(r'(highp|mediump)\s+(float)\s+(\w*[Ff]og\w*)'), r'lowp \2 \3'),
        ]
        
        for pattern, replacement in optimizations:
            old_code = code
            code = pattern.sub(replacement, code)
            if code != old_code:
                self.statistics['optimizations_applied'] += 1
        
        return code
    
    def _analyze_precision_loss(self, code: str) -> str:
        """分析精度损失"""
        # 这里可以添加精度损失分析的逻辑
        # 例如：检测可能导致精度问题的运算
        return code
    
    def _suggest_precision_improvements(self, code: str) -> str:
        """建议精度改进"""
        # 添加注释建议精度改进
        suggestions = []
        
        # 检查可能需要更高精度的操作
        if 'gl_Position' in code and 'lowp' in code:
            suggestions.append('// 建议：gl_Position相关计算使用highp精度')
        
        if 'matrix' in code.lower() and 'mediump' in code:
            suggestions.append('// 建议：矩阵运算考虑使用highp精度以避免精度损失')
        
        if suggestions:
            return '\n'.join(suggestions) + '\n' + code
        
        return code
    
    def _suggest_precision_for_variable(self, var_name: str, var_type: str, code: str) -> Optional[str]:
        """为变量建议精度"""
        var_name_lower = var_name.lower()
        
        # 基于变量名模式建议精度
        if any(pattern in var_name_lower for pattern in ['position', 'matrix', 'transform']):
            return 'highp'
        elif any(pattern in var_name_lower for pattern in ['color', 'normal', 'texcoord']):
            return 'mediump'
        elif any(pattern in var_name_lower for pattern in ['alpha', 'fog', 'blend', 'mask']):
            return 'lowp'
        
        # 基于变量类型建议精度
        if var_type in self.default_precision:
            return self.default_precision[var_type]
        
        # 分析变量在代码中的使用
        if self._is_used_in_high_precision_context(var_name, code):
            return 'highp'
        elif self._is_used_in_low_precision_context(var_name, code):
            return 'lowp'
        else:
            return 'mediump'
    
    def _determine_expression_precision(self, variables: List[str], precision_map: Dict[str, str]) -> Optional[str]:
        """确定表达式所需的精度"""
        precisions = []
        for var in variables:
            if var in precision_map:
                precisions.append(precision_map[var])
        
        if not precisions:
            return None
        
        # 返回最高精度
        return max(precisions, key=lambda x: self.precision_qualifiers.index(x))
    
    def _is_used_in_high_precision_context(self, var_name: str, code: str) -> bool:
        """检查变量是否在高精度上下文中使用"""
        high_precision_contexts = [
            'gl_Position', 'matrix', 'transform', 'normalize', 'reflect'
        ]
        
        for context in high_precision_contexts:
            if context in code and var_name in code:
                # 简单的检查，实际实现需要更复杂的分析
                return True
        
        return False
    
    def _is_used_in_low_precision_context(self, var_name: str, code: str) -> bool:
        """检查变量是否在低精度上下文中使用"""
        low_precision_contexts = [
            'mix', 'step', 'smoothstep', 'alpha', 'fog'
        ]
        
        for context in low_precision_contexts:
            if context in code and var_name in code:
                return True
        
        return False
