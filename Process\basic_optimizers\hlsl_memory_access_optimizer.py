"""
HLSL内存访问优化器 - 优化HLSL中的内存访问模式
"""
import re
from typing import Dict, List, Tuple, Set
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel

class HLSLMemoryAccessOptimizer(HLSLOptimizerBase):
    """
    内存访问优化器，专门优化HLSL中的内存访问模式。
    
    支持的优化：
    - 缓存友好的访问模式
    - 向量化内存加载
    - 内存访问合并
    - 常量缓冲区优化
    - 纹理访问优化
    - 共享内存优化（计算着色器）
    
    用法：
        optimizer = MemoryAccessOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 内存访问模式
        self.access_patterns = {
            'array_access': re.compile(r'(\w+)\[([^\]]+)\]'),
            'buffer_load': re.compile(r'(\w+)\.Load\s*\(\s*([^)]+)\s*\)'),
            'texture_load': re.compile(r'(\w+)\.Load\s*\(\s*([^)]+)\s*\)'),
            'cbuffer_access': re.compile(r'(\w+)\.(\w+)'),
            'groupshared_access': re.compile(r'groupshared\s+([^;]+);')
        }
        
        # 访问统计
        self.access_stats = {}
    
    def optimize_code(self, code: str) -> str:
        """优化内存访问代码"""
        self.reset_statistics()
        
        # 分析内存访问模式
        self._analyze_memory_access(code)
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._optimize_array_access(optimized_code)
            optimized_code = self._optimize_constant_buffer_access(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._vectorize_memory_access(optimized_code)
            optimized_code = self._merge_memory_operations(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._optimize_cache_locality(optimized_code)
            optimized_code = self._optimize_shared_memory(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _analyze_memory_access(self, code: str):
        """分析内存访问模式"""
        lines = code.splitlines()
        
        for line_num, line in enumerate(lines):
            # 分析数组访问
            array_matches = self.access_patterns['array_access'].finditer(line)
            for match in array_matches:
                array_name = match.group(1)
                index_expr = match.group(2)
                
                if array_name not in self.access_stats:
                    self.access_stats[array_name] = {
                        'type': 'array',
                        'accesses': [],
                        'access_pattern': 'unknown'
                    }
                
                self.access_stats[array_name]['accesses'].append({
                    'line': line_num,
                    'index': index_expr,
                    'type': 'read' if '=' not in line.split(match.group(0))[0] else 'write'
                })
            
            # 分析缓冲区加载
            buffer_matches = self.access_patterns['buffer_load'].finditer(line)
            for match in buffer_matches:
                buffer_name = match.group(1)
                offset_expr = match.group(2)
                
                if buffer_name not in self.access_stats:
                    self.access_stats[buffer_name] = {
                        'type': 'buffer',
                        'loads': [],
                        'access_pattern': 'unknown'
                    }
                
                self.access_stats[buffer_name]['loads'].append({
                    'line': line_num,
                    'offset': offset_expr
                })
        
        # 分析访问模式
        self._classify_access_patterns()
    
    def _classify_access_patterns(self):
        """分类访问模式"""
        for name, stats in self.access_stats.items():
            if stats['type'] == 'array' and 'accesses' in stats:
                accesses = stats['accesses']
                
                # 检查是否为顺序访问
                if self._is_sequential_access(accesses):
                    stats['access_pattern'] = 'sequential'
                # 检查是否为随机访问
                elif self._is_random_access(accesses):
                    stats['access_pattern'] = 'random'
                # 检查是否为步长访问
                elif self._is_strided_access(accesses):
                    stats['access_pattern'] = 'strided'
    
    def _optimize_array_access(self, code: str) -> str:
        """优化数组访问"""
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            optimized_line = line
            
            # 优化边界检查
            array_access = re.search(r'(\w+)\[([^\]]+)\]', line)
            if array_access:
                array_name = array_access.group(1)
                index_expr = array_access.group(2)
                
                # 如果索引是常量且在合理范围内，可以移除边界检查
                if re.match(r'^\d+$', index_expr.strip()):
                    index_val = int(index_expr.strip())
                    if 0 <= index_val < 1024:  # 假设合理的数组大小
                        # 添加注释说明已优化
                        optimized_lines.append(f"    // Optimized array access: {array_name}[{index_expr}]")
                        self.statistics['optimizations_applied'] += 1
            
            # 优化重复的数组访问
            repeated_access = re.findall(r'(\w+\[[^\]]+\])', line)
            if len(repeated_access) > 1:
                unique_accesses = list(set(repeated_access))
                if len(unique_accesses) < len(repeated_access):
                    # 有重复访问，可以缓存
                    for access in unique_accesses:
                        cache_var = f"_cached_{access.replace('[', '_').replace(']', '')}"
                        optimized_lines.append(f"    auto {cache_var} = {access};")
                        optimized_line = optimized_line.replace(access, cache_var)
                    
                    self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(optimized_line)
        
        return '\n'.join(optimized_lines)
    
    def _optimize_constant_buffer_access(self, code: str) -> str:
        """优化常量缓冲区访问"""
        lines = code.splitlines()
        optimized_lines = []
        cbuffer_cache = {}

        # 识别真正的cbuffer变量（通过多种声明方式）
        cbuffer_vars = set()
        for line in lines:
            # 查找cbuffer声明
            cbuffer_match = re.search(r'cbuffer\s+(\w+)', line)
            if cbuffer_match:
                cbuffer_vars.add(cbuffer_match.group(1))

            # 查找ConstantBuffer声明
            const_buffer_match = re.search(r'ConstantBuffer<\w+>\s+(\w+)', line)
            if const_buffer_match:
                cbuffer_vars.add(const_buffer_match.group(1))

            # 查找全局常量缓冲区变量（通常以CB、_CB等开头）
            global_cb_match = re.search(r'^\s*(\w*[Cc][Bb]\w*)\s+\w+\s*:', line)
            if global_cb_match:
                cbuffer_vars.add(global_cb_match.group(1))

        # 暂时禁用cbuffer缓存优化，因为它可能引用不存在的变量
        # 只进行简单的重复访问优化，不创建新的缓存变量

        # 查找明确存在的重复访问模式
        optimized_lines = lines.copy()

        # 只优化明确安全的模式（使用更精确的正则表达式）
        safe_optimizations = [
            # 移除多余的加0操作（使用单词边界）
            (re.compile(r'\b(\w+)\s*\+\s*0\.0\b'), r'\1'),
            (re.compile(r'\b(\w+)\s*-\s*0\.0\b'), r'\1'),
            # 移除多余的乘1操作（确保是完整的1.0，不是1.01等）
            (re.compile(r'\b(\w+)\s*\*\s*1\.0(?!\d)\b'), r'\1'),
            (re.compile(r'\b1\.0(?!\d)\s*\*\s*(\w+)\b'), r'\1'),
            # 移除多余的除1操作
            (re.compile(r'\b(\w+)\s*/\s*1\.0(?!\d)\b'), r'\1'),
        ]

        for i, line in enumerate(optimized_lines):
            for pattern, replacement in safe_optimizations:
                matches = len(pattern.findall(line))
                if matches > 0:
                    optimized_lines[i] = pattern.sub(replacement, line)
                    self.statistics['optimizations_applied'] += matches

        return '\n'.join(optimized_lines)

        for line in lines:
            optimized_line = line

            # 查找常量缓冲区访问
            cb_accesses = re.findall(r'(\w+)\.(\w+)', line)

            for cb_name, member_name in cb_accesses:
                # 只处理真正的cbuffer变量
                if cb_name in cbuffer_vars:
                    access_key = f"{cb_name}.{member_name}"

                    # 如果在同一作用域内重复访问，缓存结果
                    if access_key in cbuffer_cache:
                        cache_var = cbuffer_cache[access_key]
                        optimized_line = optimized_line.replace(access_key, cache_var)
                        self.statistics['optimizations_applied'] += 1
                    else:
                        # 创建缓存变量（使用有效的HLSL变量名）
                        safe_cb_name = re.sub(r'[^a-zA-Z0-9_]', '_', cb_name)
                        safe_member_name = re.sub(r'[^a-zA-Z0-9_]', '_', member_name)
                        cache_var = f"cached_{safe_cb_name}_{safe_member_name}"
                        cbuffer_cache[access_key] = cache_var

                        # 在函数开始处添加缓存声明（使用明确的类型而不是auto）
                        if len(optimized_lines) > 0 and '{' in optimized_lines[-1]:
                            # 推断类型（简单的类型推断）
                            var_type = self._infer_variable_type(access_key)
                            optimized_lines.append(f"    {var_type} {cache_var} = {access_key};")
                            optimized_line = optimized_line.replace(access_key, cache_var)

            optimized_lines.append(optimized_line)

        return '\n'.join(optimized_lines)
    
    def _vectorize_memory_access(self, code: str) -> str:
        """向量化内存访问"""
        lines = code.splitlines()
        optimized_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 查找连续的标量加载
            if '.Load(' in line and i + 1 < len(lines):
                current_load = re.search(r'(\w+)\s*=\s*(\w+)\.Load\s*\(\s*([^)]+)\s*\)', line)
                next_load = re.search(r'(\w+)\s*=\s*(\w+)\.Load\s*\(\s*([^)]+)\s*\)', lines[i + 1])
                
                if (current_load and next_load and 
                    current_load.group(2) == next_load.group(2)):  # 同一缓冲区
                    
                    buffer_name = current_load.group(2)
                    offset1 = current_load.group(3)
                    offset2 = next_load.group(3)
                    
                    # 检查是否为连续偏移
                    if self._are_consecutive_offsets(offset1, offset2):
                        var1 = current_load.group(1)
                        var2 = next_load.group(1)
                        
                        # 生成向量化加载
                        optimized_lines.append(f"    // Vectorized load")
                        optimized_lines.append(f"    float2 _vec_load = {buffer_name}.Load2({offset1});")
                        optimized_lines.append(f"    {var1} = _vec_load.x;")
                        optimized_lines.append(f"    {var2} = _vec_load.y;")
                        
                        self.statistics['optimizations_applied'] += 1
                        i += 2  # 跳过下一行
                        continue
            
            optimized_lines.append(line)
            i += 1
        
        return '\n'.join(optimized_lines)
    
    def _merge_memory_operations(self, code: str) -> str:
        """合并内存操作"""
        optimizations = [
            # 合并相邻的Load操作
            (re.compile(r'(\w+)\s*=\s*(\w+)\.Load\s*\(\s*(\d+)\s*\);\s*(\w+)\s*=\s*\2\.Load\s*\(\s*(\d+)\s*\);'),
             lambda m: self._merge_loads(m) if abs(int(m.group(5)) - int(m.group(3))) == 4 else m.group(0)),
            
            # 合并纹理采样
            (re.compile(r'(\w+)\s*=\s*(\w+)\.Sample\s*\(\s*([^)]+)\s*\)\.(\w);\s*(\w+)\s*=\s*\2\.Sample\s*\(\s*\3\s*\)\.(\w);'),
             r'float2 _merged_sample = \2.Sample(\3).\4\6;\n    \1 = _merged_sample.x;\n    \5 = _merged_sample.y;')
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            if callable(replacement):
                matches = list(pattern.finditer(optimized_code))
                for match in reversed(matches):
                    new_text = replacement(match)
                    if new_text != match.group(0):
                        optimized_code = optimized_code[:match.start()] + new_text + optimized_code[match.end():]
                        self.statistics['optimizations_applied'] += 1
            else:
                matches = len(pattern.findall(optimized_code))
                optimized_code = pattern.sub(replacement, optimized_code)
                self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _optimize_cache_locality(self, code: str) -> str:
        """优化缓存局部性"""
        lines = code.splitlines()
        optimized_lines = []
        
        # 查找可能的缓存不友好访问模式
        for line in lines:
            optimized_line = line
            
            # 检查数组访问模式
            array_accesses = re.findall(r'(\w+)\[([^\]]+)\]', line)
            
            for array_name, index_expr in array_accesses:
                if array_name in self.access_stats:
                    stats = self.access_stats[array_name]
                    
                    # 如果是步长访问，建议重新组织数据
                    if stats.get('access_pattern') == 'strided':
                        optimized_lines.append(f"    // OPTIMIZATION: Consider data layout reorganization for {array_name}")
                        self.statistics['optimizations_applied'] += 1
                    
                    # 如果是随机访问，建议预取
                    elif stats.get('access_pattern') == 'random':
                        optimized_lines.append(f"    // OPTIMIZATION: Consider prefetching for {array_name}")
                        self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(optimized_line)
        
        return '\n'.join(optimized_lines)
    
    def _optimize_shared_memory(self, code: str) -> str:
        """优化共享内存使用（计算着色器）"""
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            optimized_line = line
            
            # 查找groupshared声明
            groupshared_match = re.search(r'groupshared\s+([^;]+);', line)
            if groupshared_match:
                decl = groupshared_match.group(1)
                
                # 建议内存对齐
                if 'float' in decl and not '[' in decl:
                    optimized_lines.append("    // OPTIMIZATION: Consider padding for memory alignment")
                    self.statistics['optimizations_applied'] += 1
                
                # 建议bank冲突避免
                if re.search(r'\[\s*\d+\s*\]', decl):
                    optimized_lines.append("    // OPTIMIZATION: Consider bank conflict avoidance")
                    self.statistics['optimizations_applied'] += 1
            
            # 查找GroupMemoryBarrierWithGroupSync调用
            if 'GroupMemoryBarrierWithGroupSync' in line:
                optimized_lines.append("    // OPTIMIZATION: Minimize synchronization points")
            
            optimized_lines.append(optimized_line)
        
        return '\n'.join(optimized_lines)
    
    def _is_sequential_access(self, accesses: List[Dict]) -> bool:
        """检查是否为顺序访问"""
        if len(accesses) < 2:
            return False
        
        # 简化的实现：检查索引是否为递增的整数
        indices = []
        for access in accesses:
            index_str = access['index'].strip()
            if re.match(r'^\d+$', index_str):
                indices.append(int(index_str))
        
        if len(indices) < 2:
            return False
        
        # 检查是否递增
        return all(indices[i] <= indices[i+1] for i in range(len(indices)-1))
    
    def _is_random_access(self, accesses: List[Dict]) -> bool:
        """检查是否为随机访问"""
        # 简化的实现：如果不是顺序也不是步长，则认为是随机
        return not self._is_sequential_access(accesses) and not self._is_strided_access(accesses)
    
    def _is_strided_access(self, accesses: List[Dict]) -> bool:
        """检查是否为步长访问"""
        if len(accesses) < 3:
            return False
        
        # 简化的实现：检查是否有固定步长
        indices = []
        for access in accesses:
            index_str = access['index'].strip()
            if re.match(r'^\d+$', index_str):
                indices.append(int(index_str))
        
        if len(indices) < 3:
            return False
        
        # 检查步长是否一致
        stride = indices[1] - indices[0]
        return all(indices[i+1] - indices[i] == stride for i in range(len(indices)-1))
    
    def _are_consecutive_offsets(self, offset1: str, offset2: str) -> bool:
        """检查两个偏移是否连续"""
        try:
            off1 = int(offset1.strip())
            off2 = int(offset2.strip())
            return abs(off2 - off1) == 4  # 假设float大小为4字节
        except ValueError:
            return False
    
    def _merge_loads(self, match) -> str:
        """合并两个Load操作"""
        var1, buffer, offset1, var2, offset2 = match.groups()

        # 生成Load2操作
        merged = f"{var1} = {buffer}.Load2({offset1}).x;\n    {var2} = {buffer}.Load2({offset1}).y;"
        return merged

    def _infer_variable_type(self, access_key: str) -> str:
        """推断变量类型"""
        # 简单的类型推断逻辑
        if 'Matrix' in access_key or 'matrix' in access_key:
            return 'float4x4'
        elif 'Color' in access_key or 'color' in access_key:
            return 'float4'
        elif 'Position' in access_key or 'position' in access_key:
            return 'float3'
        elif 'Direction' in access_key or 'direction' in access_key:
            return 'float3'
        elif 'Normal' in access_key or 'normal' in access_key:
            return 'float3'
        elif 'UV' in access_key or 'uv' in access_key or 'TexCoord' in access_key:
            return 'float2'
        elif 'Time' in access_key or 'time' in access_key:
            return 'float'
        else:
            # 默认类型
            return 'float4'
