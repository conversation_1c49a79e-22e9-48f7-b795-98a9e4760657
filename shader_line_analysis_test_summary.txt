🎯 语法树分析摘要报告
==================================================
📊 基本统计:
  总代码行: 762
  变量声明: 591
  运算过程: 0
  临时变量: 0

🔍 类型分析:
  类型转换: 0
  精度问题: 0

🎨 类型分布:
  bool: 9 (1.5%)
  float: 207 (35.0%)
  float2: 36 (6.1%)
  float3: 146 (24.7%)
  float3x3: 5 (0.8%)
  float4: 4 (0.7%)
  half: 65 (11.0%)
  half3: 37 (6.3%)
  half4: 22 (3.7%)
  int: 4 (0.7%)
  uint: 56 (9.5%)

📈 语法树分析特点:
  基于AST的精确语法解析
  完整的运算过程分解
  临时变量类型推断
  向量成员访问支持

💡 性能建议:
  ✅ 代码类型使用良好，无明显性能问题