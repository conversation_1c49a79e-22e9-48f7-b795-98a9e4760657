import re

class HalfFloatConverter:
    """
    HLSL float/half 类型转换优化器。
    用法：
        converter = HalfFloatConverter()
        optimized_code = converter.optimize_code(hlsl_code)
    """
    # 支持的类型
    TYPES = ['float', 'float2', 'float3', 'float4', 'float2x2', 'float3x3', 'float4x4',
             'half', 'half2', 'half3', 'half4', 'half2x2', 'half3x3', 'half4x4']
    # 替换方向
    DIRECTION_HALF2FLOAT = 'half2float'
    DIRECTION_FLOAT2HALF = 'float2half'
    # 替换方式
    METHOD_GLOBAL = 'global'
    METHOD_LOCAL = 'local'
    METHOD_GLOBAL_VAR = 'global_var'
    METHOD_PARAM = 'param'
    METHOD_RETURN = 'return'
    METHOD_ANNOTATED = 'annotated'

    def _replace_type(self, text, src, dst):
        pattern = re.compile(rf'\b{src}(\d*[x\d]*)?\b')
        return pattern.sub(lambda m: m.group(0).replace(src, dst), text)

    def optimize_code(self, code: str, direction='float2half', method='global') -> str:
        """
        direction: 'half2float' or 'float2half'
        method: 'global', 'local', 'global_var', 'param', 'return', 'annotated'
        """
        if direction == self.DIRECTION_HALF2FLOAT:
            src, dst = 'half', 'float'
        else:
            src, dst = 'float', 'half'
        if method == self.METHOD_GLOBAL:
            return self._replace_type(code, src, dst)
        lines = code.splitlines(keepends=True)
        new_lines = []
        in_function = False
        for line in lines:
            func_def_match = re.match(r'\s*\w+\s+\w+\s*\(([^)]*)\)', line)
            if func_def_match:
                in_function = True
                params = func_def_match.group(1)
                if method == self.METHOD_PARAM:
                    def param_replacer(m):
                        return m.group(0).replace(src, dst)
                    new_params = re.sub(rf'\b{src}(\d*[x\d]*)?\b', param_replacer, params)
                    line = line.replace(params, new_params)
                if method == self.METHOD_RETURN:
                    line = re.sub(rf'^\s*{src}(\d*[x\d]*)?', lambda m: m.group(0).replace(src, dst), line)
            if '}' in line and in_function:
                in_function = False
            if method == self.METHOD_LOCAL and in_function:
                line = re.sub(rf'\b{src}(\d*[x\d]*)?\b', lambda m: m.group(0).replace(src, dst), line)
            if method == self.METHOD_GLOBAL_VAR and not in_function:
                line = re.sub(rf'\b{src}(\d*[x\d]*)?\b', lambda m: m.group(0).replace(src, dst), line)
            if method == self.METHOD_ANNOTATED and f'// @{dst}' in line:
                line = re.sub(rf'\b{src}(\d*[x\d]*)?\b', lambda m: m.group(0).replace(src, dst), line)
            new_lines.append(line)
        return ''.join(new_lines) 