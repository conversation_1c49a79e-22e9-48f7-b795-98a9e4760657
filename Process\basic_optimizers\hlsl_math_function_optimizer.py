"""
HLSL数学函数优化器 - 优化HLSL中的数学运算和函数调用
"""
import re
from typing import Dict, List, Tuple
from .hlsl_optimizer_base import HLSLOptimizerBase, OptimizationLevel

class HLSLMathFunctionOptimizer(HLSLOptimizerBase):
    """
    数学函数优化器，专门优化HLSL中的数学运算和函数调用。
    
    支持的优化：
    - 三角函数快速近似
    - 幂函数优化
    - 平方根和倒数平方根优化
    - 向量运算优化
    - 常量折叠
    - 数学恒等式应用
    
    用法：
        optimizer = MathFunctionOptimizer()
        optimized_code = optimizer.optimize_code(hlsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        super().__init__(optimization_level)
        
        # 数学函数替换映射
        self.fast_math_replacements = {
            # 快速三角函数近似
            'sin': {
                OptimizationLevel.AGGRESSIVE: 'sin_fast',
                OptimizationLevel.MAXIMUM: 'sin_approx'
            },
            'cos': {
                OptimizationLevel.AGGRESSIVE: 'cos_fast', 
                OptimizationLevel.MAXIMUM: 'cos_approx'
            },
            'tan': {
                OptimizationLevel.MAXIMUM: 'tan_approx'
            },
            # 快速指数和对数
            'exp': {
                OptimizationLevel.MAXIMUM: 'exp_fast'
            },
            'log': {
                OptimizationLevel.MAXIMUM: 'log_fast'
            }
        }
    
    def optimize_code(self, code: str) -> str:
        """优化数学函数代码"""
        self.reset_statistics()
        
        optimized_code = code
        
        if self.optimization_level.value >= OptimizationLevel.BASIC.value:
            optimized_code = self._optimize_power_functions(optimized_code)
            optimized_code = self._optimize_sqrt_functions(optimized_code)
            # 暂时禁用常量折叠，直到修复变量声明问题
            # optimized_code = self._fold_constants_safe(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.AGGRESSIVE.value:
            optimized_code = self._apply_math_identities(optimized_code)
            optimized_code = self._optimize_vector_operations(optimized_code)
            optimized_code = self._replace_expensive_functions(optimized_code)
        
        if self.optimization_level.value >= OptimizationLevel.MAXIMUM.value:
            optimized_code = self._use_fast_approximations(optimized_code)
            optimized_code = self._optimize_transcendental_functions(optimized_code)
        
        self.statistics['lines_processed'] = len(code.splitlines())
        return optimized_code
    
    def _optimize_power_functions(self, code: str) -> str:
        """优化幂函数"""
        optimizations = [
            # pow(x, 2) -> x * x
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*2(?:\.0)?\s*\)'), r'((\1) * (\1))'),
            
            # pow(x, 3) -> x * x * x
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*3(?:\.0)?\s*\)'), r'((\1) * (\1) * (\1))'),
            
            # pow(x, 4) -> (x * x) * (x * x) (避免使用auto关键字)
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*4(?:\.0)?\s*\)'),
             lambda m: f'(({m.group(1)}) * ({m.group(1)}) * ({m.group(1)}) * ({m.group(1)}))'),
            
            # pow(x, 0.5) -> sqrt(x)
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*0\.5\s*\)'), r'sqrt(\1)'),
            
            # pow(x, -0.5) -> rsqrt(x)
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*-0\.5\s*\)'), r'rsqrt(\1)'),
            
            # pow(x, -1) -> rcp(x) or 1.0/x
            (re.compile(r'pow\s*\(\s*([^,]+)\s*,\s*-1(?:\.0)?\s*\)'), r'rcp(\1)'),
            
            # pow(2, x) -> exp2(x)
            (re.compile(r'pow\s*\(\s*2(?:\.0)?\s*,\s*([^)]+)\s*\)'), r'exp2(\1)'),
            
            # pow(e, x) -> exp(x)
            (re.compile(r'pow\s*\(\s*2\.71828\w*\s*,\s*([^)]+)\s*\)'), r'exp(\1)'),
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            if callable(replacement):
                matches = list(pattern.finditer(optimized_code))
                for match in reversed(matches):
                    new_text = replacement(match)
                    optimized_code = optimized_code[:match.start()] + new_text + optimized_code[match.end():]
                    self.statistics['optimizations_applied'] += 1
            else:
                matches = len(pattern.findall(optimized_code))
                optimized_code = pattern.sub(replacement, optimized_code)
                self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _optimize_sqrt_functions(self, code: str) -> str:
        """优化平方根函数"""
        optimizations = [
            # sqrt(x * x + y * y) -> length(float2(x, y))
            (re.compile(r'sqrt\s*\(\s*([^+]+)\s*\*\s*\1\s*\+\s*([^)]+)\s*\*\s*\2\s*\)'),
             r'length(float2(\1, \2))'),
            
            # sqrt(x * x + y * y + z * z) -> length(float3(x, y, z))
            (re.compile(r'sqrt\s*\(\s*([^+]+)\s*\*\s*\1\s*\+\s*([^+]+)\s*\*\s*\2\s*\+\s*([^)]+)\s*\*\s*\3\s*\)'),
             r'length(float3(\1, \2, \3))'),
            
            # 1.0 / sqrt(x) -> rsqrt(x)
            (re.compile(r'1\.0\s*/\s*sqrt\s*\(\s*([^)]+)\s*\)'), r'rsqrt(\1)'),
            (re.compile(r'1\s*/\s*sqrt\s*\(\s*([^)]+)\s*\)'), r'rsqrt(\1)'),
            
            # sqrt(1.0 / x) -> rsqrt(x)
            (re.compile(r'sqrt\s*\(\s*1\.0\s*/\s*([^)]+)\s*\)'), r'rsqrt(\1)'),
            
            # sqrt(x) * sqrt(x) -> x (当x >= 0时)
            (re.compile(r'sqrt\s*\(\s*([^)]+)\s*\)\s*\*\s*sqrt\s*\(\s*\1\s*\)'), r'(\1)'),
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _fold_constants(self, code: str) -> str:
        """常量折叠"""
        import math
        
        def evaluate_math_expr(match):
            expr = match.group(0)
            
            # 简单的常量表达式求值
            try:
                # 替换HLSL函数为Python等价函数
                python_expr = expr
                python_expr = re.sub(r'\bsin\b', 'math.sin', python_expr)
                python_expr = re.sub(r'\bcos\b', 'math.cos', python_expr)
                python_expr = re.sub(r'\btan\b', 'math.tan', python_expr)
                python_expr = re.sub(r'\bsqrt\b', 'math.sqrt', python_expr)
                python_expr = re.sub(r'\bexp\b', 'math.exp', python_expr)
                python_expr = re.sub(r'\blog\b', 'math.log', python_expr)
                python_expr = re.sub(r'\bpow\b', 'math.pow', python_expr)
                
                # 只处理纯数字表达式
                if re.match(r'^[math.\d\s+\-*/().]+$', python_expr):
                    result = eval(python_expr)
                    self.statistics['optimizations_applied'] += 1
                    # 确保结果是有效的浮点数格式
                    if isinstance(result, (int, float)):
                        return f"{float(result):.6f}".rstrip('0').rstrip('.')
                    return str(result)
            except:
                pass
            
            return expr
        
        # 查找常量数学表达式（更严格的模式匹配）
        const_math_pattern = re.compile(r'(?:sin|cos|tan|sqrt|exp|log|pow)\s*\([\d\s+\-*/.()]+\)')
        optimized_code = const_math_pattern.sub(evaluate_math_expr, code)
        
        return optimized_code

    def _fold_constants_safe(self, code: str) -> str:
        """安全的常量折叠，避免无限循环和未声明变量"""
        optimizations = [
            # 数学恒等式（不创建新变量）
            (re.compile(r'\b(\w+)\s*\*\s*1(?:\.0)?\b'), r'\1'),
            (re.compile(r'\b1(?:\.0)?\s*\*\s*(\w+)\b'), r'\1'),
            (re.compile(r'\b(\w+)\s*\+\s*0(?:\.0)?\b'), r'\1'),
            (re.compile(r'\b0(?:\.0)?\s*\+\s*(\w+)\b'), r'\1'),
            (re.compile(r'\b(\w+)\s*-\s*0(?:\.0)?\b'), r'\1'),
            (re.compile(r'\b(\w+)\s*/\s*1(?:\.0)?\b'), r'\1'),

            # 三元运算符常量优化
            (re.compile(r'\btrue\s*\?\s*([^:]+)\s*:\s*[^;]+'), r'\1'),
            (re.compile(r'\bfalse\s*\?\s*[^:]+\s*:\s*([^;]+)'), r'\1'),

            # 简单的常量计算（直接替换，不创建变量）
            (re.compile(r'\b2\.0\s*\*\s*2\.0\b'), '4.0'),
            (re.compile(r'\b3\.0\s*\+\s*2\.0\b'), '5.0'),
            (re.compile(r'\b1\.0\s*\+\s*2\.0\b'), '3.0'),
            (re.compile(r'\b5\.0\s*-\s*2\.0\b'), '3.0'),
            (re.compile(r'\b10\.0\s*/\s*2\.0\b'), '5.0'),
        ]

        optimized_code = code
        for pattern, replacement in optimizations:
            if callable(replacement):
                matches = list(pattern.finditer(optimized_code))
                for match in reversed(matches):
                    try:
                        new_text = replacement(match)
                        if new_text != match.group(0):
                            optimized_code = optimized_code[:match.start()] + new_text + optimized_code[match.end():]
                            self.statistics['optimizations_applied'] += 1
                    except:
                        continue  # 跳过有问题的替换
            else:
                matches = len(pattern.findall(optimized_code))
                optimized_code = pattern.sub(replacement, optimized_code)
                self.statistics['optimizations_applied'] += matches

        return optimized_code

    def _apply_math_identities(self, code: str) -> str:
        """应用数学恒等式（只使用安全的优化）"""
        optimizations = [
            # sin^2 + cos^2 = 1 的相关优化
            (re.compile(r'sin\s*\(\s*([^)]+)\s*\)\s*\*\s*sin\s*\(\s*\1\s*\)\s*\+\s*cos\s*\(\s*\1\s*\)\s*\*\s*cos\s*\(\s*\1\s*\)'),
             r'1.0'),

            # tan(x) = sin(x) / cos(x) -> 使用内置tan
            (re.compile(r'sin\s*\(\s*([^)]+)\s*\)\s*/\s*cos\s*\(\s*\1\s*\)'),
             r'tan(\1)'),

            # 暂时禁用 x * 0 = 0 优化，因为正则表达式有问题
            # (re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\*\s*0(?:\.0)?\b'), r'0.0'),
            # (re.compile(r'\b0(?:\.0)?\s*\*\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b'), r'0.0'),

            # 暂时禁用可能有问题的优化
            # x * 1 = x, x + 0 = x, x - 0 = x
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _optimize_vector_operations(self, code: str) -> str:
        """优化向量运算"""
        optimizations = [
            # dot(v, v) -> 保持不变，因为HLSL没有lengthsq函数
            # (re.compile(r'dot\s*\(\s*([^,]+)\s*,\s*\1\s*\)'), r'lengthsq(\1)'),
            
            # normalize(v) * length(v) -> v
            (re.compile(r'normalize\s*\(\s*([^)]+)\s*\)\s*\*\s*length\s*\(\s*\1\s*\)'), r'(\1)'),
            
            # length(normalize(v)) -> 1.0
            (re.compile(r'length\s*\(\s*normalize\s*\(\s*[^)]+\s*\)\s*\)'), r'1.0'),
            
            # cross(v, v) -> float3(0, 0, 0)
            (re.compile(r'cross\s*\(\s*([^,]+)\s*,\s*\1\s*\)'), r'float3(0.0, 0.0, 0.0)'),
            
            # lerp(a, b, 0) -> a
            (re.compile(r'lerp\s*\(\s*([^,]+)\s*,\s*[^,]+\s*,\s*0(?:\.0)?\s*\)'), r'\1'),
            
            # lerp(a, b, 1) -> b
            (re.compile(r'lerp\s*\(\s*[^,]+\s*,\s*([^,]+)\s*,\s*1(?:\.0)?\s*\)'), r'\1'),
            
            # lerp(a, a, t) -> a
            (re.compile(r'lerp\s*\(\s*([^,]+)\s*,\s*\1\s*,\s*[^)]+\s*\)'), r'\1'),
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _replace_expensive_functions(self, code: str) -> str:
        """替换昂贵的函数调用"""
        optimizations = [
            # atan2(y, x) 在某些情况下可以简化
            (re.compile(r'atan2\s*\(\s*([^,]+)\s*,\s*\1\s*\)'), r'(PI / 4.0)'),  # atan2(x, x) = π/4
            
            # asin(0) = 0, acos(1) = 0, atan(0) = 0
            (re.compile(r'asin\s*\(\s*0(?:\.0)?\s*\)'), r'0.0'),
            (re.compile(r'acos\s*\(\s*1(?:\.0)?\s*\)'), r'0.0'),
            (re.compile(r'atan\s*\(\s*0(?:\.0)?\s*\)'), r'0.0'),
            
            # sin(0) = 0, cos(0) = 1, tan(0) = 0
            (re.compile(r'sin\s*\(\s*0(?:\.0)?\s*\)'), r'0.0'),
            (re.compile(r'cos\s*\(\s*0(?:\.0)?\s*\)'), r'1.0'),
            (re.compile(r'tan\s*\(\s*0(?:\.0)?\s*\)'), r'0.0'),
            
            # exp(0) = 1, log(1) = 0
            (re.compile(r'exp\s*\(\s*0(?:\.0)?\s*\)'), r'1.0'),
            (re.compile(r'log\s*\(\s*1(?:\.0)?\s*\)'), r'0.0'),
        ]
        
        optimized_code = code
        for pattern, replacement in optimizations:
            matches = len(pattern.findall(optimized_code))
            optimized_code = pattern.sub(replacement, optimized_code)
            self.statistics['optimizations_applied'] += matches
        
        return optimized_code
    
    def _use_fast_approximations(self, code: str) -> str:
        """使用快速近似函数"""
        if self.optimization_level == OptimizationLevel.MAXIMUM:
            # 添加快速近似函数的定义
            fast_functions = """
// Fast approximation functions
float sin_approx(float x) {
    x = fmod(x + PI, 2.0 * PI) - PI;
    return x * (1.0 - abs(x) / PI);
}

float cos_approx(float x) {
    return sin_approx(x + PI * 0.5);
}

float exp_fast(float x) {
    x = 1.0 + x / 256.0;
    x *= x; x *= x; x *= x; x *= x;
    x *= x; x *= x; x *= x; x *= x;
    return x;
}
"""
            
            # 在代码开头添加快速函数定义
            optimized_code = fast_functions + "\n" + code
            
            # 替换函数调用
            for func_name, replacements in self.fast_math_replacements.items():
                if self.optimization_level in replacements:
                    fast_func = replacements[self.optimization_level]
                    pattern = re.compile(rf'\b{func_name}\s*\(')
                    matches = len(pattern.findall(optimized_code))
                    optimized_code = pattern.sub(f'{fast_func}(', optimized_code)
                    self.statistics['optimizations_applied'] += matches
            
            return optimized_code
        
        return code
    
    def _optimize_transcendental_functions(self, code: str) -> str:
        """优化超越函数"""
        optimizations = [
            # 使用更快的近似
            # 这里可以添加更多的超越函数优化
            
            # 对于小角度，sin(x) ≈ x, cos(x) ≈ 1 - x²/2
            # 但这需要运行时检查，所以这里只是添加注释提示
        ]
        
        lines = code.splitlines()
        optimized_lines = []
        
        for line in lines:
            # 检查是否有超越函数调用
            if any(func in line for func in ['sin', 'cos', 'tan', 'exp', 'log']):
                optimized_lines.append('    // Consider small-angle approximations for better performance')
                self.statistics['optimizations_applied'] += 1
            
            optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
