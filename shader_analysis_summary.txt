🎯 语法树分析摘要报告
==================================================
📊 基本统计:
  总代码行: 762
  变量声明: 1029
  运算过程: 2172
  临时变量: 1483

🔍 类型分析:
  类型转换: 624
  精度问题: 0

🎨 类型分布:
  bool: 9 (0.9%)
  float: 382 (37.1%)
  float2: 62 (6.0%)
  float3: 226 (22.0%)
  float3x3: 1 (0.1%)
  float4: 21 (2.0%)
  half: 146 (14.2%)
  half3: 88 (8.6%)
  half4: 31 (3.0%)
  int: 4 (0.4%)
  uint: 59 (5.7%)

📈 语法树分析特点:
  基于AST的精确语法解析
  完整的运算过程分解
  临时变量类型推断
  向量成员访问支持

💡 性能建议:
  ⚠️  发现 624 个类型转换，建议优化