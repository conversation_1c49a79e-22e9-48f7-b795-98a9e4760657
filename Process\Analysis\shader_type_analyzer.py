#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于语法树的精确类型分析器
"""

from typing import Dict, List, Any

from .syntax_tree_builder import SyntaxTreeAnalyzer, LineAnalysisResult

class ShaderTypeAnalyzer:
    """基于语法树的类型分析器"""

    def __init__(self):
        self.syntax_analyzer = SyntaxTreeAnalyzer()
    


    def analyze_shader_with_precise_types(self, shader_content: str) -> Dict:
        """分析着色器代码并进行精确类型分析"""
        # 使用语法树分析器进行完整分析
        syntax_result = self.syntax_analyzer.analyze_shader_with_syntax_trees(shader_content)


        # 整合结果
        result = {
            'code_lines': syntax_result['code_lines'],
            'line_analyses': syntax_result['line_analyses'],
            'global_type_table': syntax_result['global_type_table'],
            'overall_statistics': self._generate_overall_statistics(syntax_result['line_analyses'])
        }

        return result

    def _generate_overall_statistics(self, line_analyses: List[LineAnalysisResult]) -> Dict:
        """生成整体统计信息"""
        overall_stats = {
            'total_lines': len(line_analyses),
            'total_variables': 0,
            'total_operations': 0,
            'total_temp_variables': 0,
            'total_type_conversions': 0,
            'total_precision_issues': 0,
            'type_distribution': {},
        }

        temp_var_count = 0

        for analysis in line_analyses:
            # 统计变量声明
            overall_stats['total_variables'] += len(analysis.variable_types)

            # 统计运算过程
            overall_stats['total_operations'] += len(analysis.operation_process)

            # 统计类型转换
            overall_stats['total_type_conversions'] += len(analysis.type_conversions)

            # 统计精度问题
            overall_stats['total_precision_issues'] += len(analysis.precision_issues)

            # 统计类型分布（从变量类型中）
            for _, var_type in analysis.variable_types.items():
                if hasattr(var_type, 'value'):
                    type_name = var_type.value
                else:
                    type_name = str(var_type)
                overall_stats['type_distribution'][type_name] = \
                    overall_stats['type_distribution'].get(type_name, 0) + 1

            # 统计临时变量（从运算过程中）
            for op in analysis.operation_process:
                if op.string.startswith('tmp_'):
                    temp_var_count += 1

        overall_stats['total_temp_variables'] = temp_var_count

        return overall_stats

