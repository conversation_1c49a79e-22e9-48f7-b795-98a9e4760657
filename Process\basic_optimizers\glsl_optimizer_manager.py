"""
GLSL优化器管理器 - 统一管理所有GLSL优化器
"""
import re
from typing import Dict, List, Optional, Any
from .glsl_optimizer_base import GLSLOptimizerBase, OptimizationLevel

# 导入所有GLSL优化器
from .glsl_texture_sampling_optimizer import GLSLTextureSamplingOptimizer
from .glsl_loop_unroll_optimizer import GLSLLoopUnrollOptimizer
from .glsl_branch_optimizer import GLSLBranchOptimizer
from .glsl_memory_access_optimizer import G<PERSON>LMemoryAccessOptimizer
from .glsl_math_function_optimizer import GLSLMathFunctionOptimizer
from .glsl_vectorization_optimizer import G<PERSON><PERSON>VectorizationOptimizer
from .glsl_precision_analyzer import GLSLPrecisionAnalyzer
from .glsl_register_allocation_optimizer import GLSLRegisterAllocationOptimizer

class GLSLOptimizerManager:
    """
    GLSL优化器管理器，统一管理和调用所有GLSL优化器。
    
    支持的优化器：
    - 纹理采样优化器
    - 循环展开优化器
    - 分支优化器
    - 内存访问优化器
    - 数学函数优化器
    - 向量化优化器
    - 精度分析优化器
    - 寄存器分配优化器
    
    用法：
        manager = GLSLOptimizerManager()
        optimized_code = manager.optimize_all(glsl_code)
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BASIC):
        self.optimization_level = optimization_level
        
        # 初始化所有GLSL优化器
        self.optimizers = {
            'texture_sampling': GLSLTextureSamplingOptimizer(optimization_level),
            'loop_unroll': GLSLLoopUnrollOptimizer(optimization_level),
            'branch': GLSLBranchOptimizer(optimization_level),
            'memory_access': GLSLMemoryAccessOptimizer(optimization_level),
            'math_function': GLSLMathFunctionOptimizer(optimization_level),
            'vectorization': GLSLVectorizationOptimizer(optimization_level),
            'precision_analyzer': GLSLPrecisionAnalyzer(optimization_level),
            'register_allocation': GLSLRegisterAllocationOptimizer(optimization_level)
        }
        
        # 优化器执行顺序（针对GLSL特性优化）
        self.optimization_order = [
            'precision_analyzer',      # 首先分析精度需求（GLSL精度限定符）
            'math_function',           # 优化数学函数
            'vectorization',           # 向量化操作（包括swizzle优化）
            'memory_access',           # 优化内存访问
            'texture_sampling',        # 优化纹理采样
            'branch',                  # 优化分支（使用mix等GLSL函数）
            'loop_unroll',            # 循环展开
            'register_allocation'      # 最后优化寄存器分配
        ]
        
        # 统计信息
        self.total_statistics = {}
    
    def optimize_all(self, code: str, selected_optimizers: Optional[List[str]] = None) -> str:
        """
        使用所有或指定的优化器优化代码
        
        Args:
            code: 输入的GLSL代码
            selected_optimizers: 要使用的优化器列表，None表示使用所有优化器
            
        Returns:
            优化后的GLSL代码
        """
        optimized_code = code
        self.total_statistics = {}
        
        # 确定要使用的优化器
        if selected_optimizers is None:
            selected_optimizers = self.optimization_order
        
        # 按顺序执行优化
        for optimizer_name in self.optimization_order:
            if optimizer_name in selected_optimizers and optimizer_name in self.optimizers:
                optimizer = self.optimizers[optimizer_name]
                
                print(f"Applying {optimizer_name} optimizer...")
                
                try:
                    # 执行优化
                    optimized_code = optimizer.optimize_code(optimized_code)
                    
                    # 收集统计信息
                    if hasattr(optimizer, 'get_statistics'):
                        stats = optimizer.get_statistics()
                        self.total_statistics[optimizer_name] = stats
                    
                    print(f"✓ {optimizer_name} completed")
                    
                except Exception as e:
                    print(f"✗ {optimizer_name} failed: {str(e)}")
                    continue
        
        return optimized_code
    
    def optimize_single(self, code: str, optimizer_name: str, **kwargs) -> str:
        """
        使用单个优化器优化代码
        
        Args:
            code: 输入的GLSL代码
            optimizer_name: 优化器名称
            **kwargs: 传递给优化器的额外参数
            
        Returns:
            优化后的GLSL代码
        """
        if optimizer_name not in self.optimizers:
            raise ValueError(f"Unknown optimizer: {optimizer_name}")
        
        optimizer = self.optimizers[optimizer_name]
        return optimizer.optimize_code(code)
    
    def get_available_optimizers(self) -> List[str]:
        """获取可用的优化器列表"""
        return list(self.optimizers.keys())
    
    def get_optimizer_info(self, optimizer_name: str) -> Dict[str, Any]:
        """获取优化器信息"""
        if optimizer_name not in self.optimizers:
            return {}
        
        optimizer = self.optimizers[optimizer_name]
        
        info = {
            'name': optimizer_name,
            'class': optimizer.__class__.__name__,
            'description': optimizer.__doc__.split('\n')[1].strip() if optimizer.__doc__ else "No description",
            'optimization_level': getattr(optimizer, 'optimization_level', None)
        }
        
        return info
    
    def get_total_statistics(self) -> Dict[str, Any]:
        """获取所有优化器的统计信息"""
        total_optimizations = 0
        total_lines = 0
        
        for optimizer_name, stats in self.total_statistics.items():
            if 'optimizations_applied' in stats:
                total_optimizations += stats['optimizations_applied']
            if 'lines_processed' in stats:
                total_lines = max(total_lines, stats['lines_processed'])
        
        return {
            'total_optimizations_applied': total_optimizations,
            'total_lines_processed': total_lines,
            'optimizers_used': len(self.total_statistics),
            'individual_stats': self.total_statistics
        }
    
    def set_optimization_level(self, level: OptimizationLevel):
        """设置所有优化器的优化级别"""
        self.optimization_level = level
        
        # 更新所有支持优化级别的优化器
        for optimizer_name, optimizer in self.optimizers.items():
            if hasattr(optimizer, 'optimization_level'):
                optimizer.optimization_level = level
    
    def create_optimization_profile(self, profile_name: str) -> List[str]:
        """创建优化配置文件"""
        profiles = {
            'fast': [
                'precision_analyzer',
                'math_function'
            ],
            'balanced': [
                'precision_analyzer',
                'math_function',
                'vectorization',
                'branch'
            ],
            'aggressive': [
                'precision_analyzer',
                'math_function',
                'vectorization',
                'memory_access',
                'texture_sampling',
                'branch',
                'loop_unroll',
                'register_allocation'
            ],
            'mobile_optimized': [
                'precision_analyzer',  # 重要：移动端GPU精度优化
                'vectorization',
                'texture_sampling',
                'branch'
            ],
            'precision_focused': [
                'precision_analyzer',
                'math_function'
            ]
        }
        
        return profiles.get(profile_name, self.optimization_order)
    
    def optimize_with_profile(self, code: str, profile_name: str) -> str:
        """使用预定义的优化配置文件优化代码"""
        selected_optimizers = self.create_optimization_profile(profile_name)
        return self.optimize_all(code, selected_optimizers)
    
    def analyze_code_characteristics(self, code: str) -> Dict[str, Any]:
        """分析GLSL代码特征，建议最适合的优化器"""
        characteristics = {
            'has_texture_sampling': bool(re.search(r'\btexture\s*\(', code)),
            'has_loops': bool(re.search(r'\bfor\s*\(', code)),
            'has_branches': bool(re.search(r'\bif\s*\(', code)),
            'has_math_functions': bool(re.search(r'\b(sin|cos|tan|sqrt|pow|exp|log|normalize|length|dot|cross)\s*\(', code)),
            'has_vector_operations': bool(re.search(r'\bvec[234]\b', code)),
            'has_precision_qualifiers': bool(re.search(r'\b(highp|mediump|lowp)\b', code)),
            'has_swizzle_operations': bool(re.search(r'\.[xyzwrgba]+\b', code)),
            'shader_stage': self._detect_shader_stage(code),
            'line_count': len(code.splitlines()),
            'complexity_score': self._calculate_complexity_score(code)
        }
        
        # 基于特征推荐优化器
        recommended_optimizers = []
        
        if characteristics['has_texture_sampling']:
            recommended_optimizers.append('texture_sampling')
        
        if characteristics['has_loops']:
            recommended_optimizers.append('loop_unroll')
        
        if characteristics['has_branches']:
            recommended_optimizers.append('branch')
        
        if characteristics['has_math_functions']:
            recommended_optimizers.append('math_function')
        
        if characteristics['has_vector_operations'] or characteristics['has_swizzle_operations']:
            recommended_optimizers.append('vectorization')
        
        # GLSL总是建议精度分析
        recommended_optimizers.append('precision_analyzer')
        
        if characteristics['complexity_score'] > 30:  # GLSL阈值较低
            recommended_optimizers.append('register_allocation')
        
        characteristics['recommended_optimizers'] = list(set(recommended_optimizers))
        
        return characteristics
    
    def _detect_shader_stage(self, code: str) -> str:
        """检测GLSL着色器阶段"""
        if 'gl_Position' in code or 'gl_VertexID' in code:
            return 'vertex'
        elif 'gl_FragColor' in code or 'gl_FragCoord' in code or 'gl_FragDepth' in code:
            return 'fragment'
        elif 'gl_InvocationID' in code or 'EmitVertex' in code:
            return 'geometry'
        elif 'gl_TessCoord' in code:
            return 'tessellation'
        elif 'gl_NumWorkGroups' in code or 'gl_WorkGroupID' in code:
            return 'compute'
        else:
            return 'unknown'
    
    def _calculate_complexity_score(self, code: str) -> int:
        """计算GLSL代码复杂度分数"""
        score = 0
        
        # 基于不同元素计算复杂度
        score += len(re.findall(r'\bfor\s*\(', code)) * 5  # 循环
        score += len(re.findall(r'\bif\s*\(', code)) * 3   # 分支
        score += len(re.findall(r'\b(sin|cos|tan|sqrt|pow|exp|log|normalize|length|dot|cross)\s*\(', code)) * 4  # 数学函数
        score += len(re.findall(r'\btexture\s*\(', code)) * 6  # 纹理采样
        score += len(re.findall(r'\bvec[234]\b', code)) * 2  # 向量类型
        score += len(re.findall(r'\bmat[234]\b', code)) * 3  # 矩阵类型
        score += len(code.splitlines()) // 10  # 代码行数
        
        return score
