# Compiler Explorer (Godbolt) HLSL API 调用示例

> 官方API文档: https://github.com/compiler-explorer/compiler-explorer/blob/main/docs/API.md

本文件展示如何通过 HTTP 请求直接调用 Godbolt Compiler Explorer API 编译 HLSL，包括 curl、httpie 等命令行工具的实际用法。

---

## 1. 获取支持的编译器列表

```sh
curl https://godbolt.org/api/compilers
```

---

## 2. 编译HLSL源码（POST /api/compiler/{compiler}/compile）

### 2.1 curl 示例

```sh
curl -X POST \
  https://godbolt.org/api/compiler/dxctrunk/compile \
  -H "Content-Type: application/json" \
  -d '{
    "source": "float4 main(float2 uv : TEXCOORD) : SV_Target { return float4(uv, 0, 1); }",
    "options": { "userArguments": "-T ps_6_0 -E main -O3" }
  }'
```

### 2.2 httpie 示例

```sh
http POST https://godbolt.org/api/compiler/dxctrunk/compile \
  source='float4 main(float2 uv : TEXCOORD) : SV_Target { return float4(uv, 0, 1); }' \
  options:='{"userArguments": "-T ps_6_0 -E main -O3"}'
```

---

## 3. 编译本地HLSL文件（curl上传文件内容）

```sh
curl -X POST \
  https://godbolt.org/api/compiler/dxctrunk/compile \
  -H "Content-Type: application/json" \
  -d @<(echo '{"source": "'$(cat YourShader.hlsl | sed ':a;N;$!ba;s/\n/\\n/g')'", "options": {"userArguments": "-T ps_6_0 -E main"}}')
```
*注：此命令在Linux/macOS下可用，Windows下建议用Python或手动拼接JSON。*

---

## 4. 返回格式说明

- `asm`: 汇编输出（list，每行一个dict，含text字段）
- `stderr`: 编译器标准错误输出
- `stdout`: 编译器标准输出
- `code`: 返回码（0为成功）

示例返回：

```json
{
  "asm": [
    {"text": "; dxc version ..."},
    {"text": "..."}
  ],
  "stderr": "",
  "stdout": "",
  "code": 0
}
```

---

## 5. 常用参数说明
- `source`：HLSL源码字符串
- `options.userArguments`：编译参数（如 `-T ps_6_0 -E main -O3`）

---

## 6. 参考
- [Compiler Explorer API官方文档](https://github.com/compiler-explorer/compiler-explorer/blob/main/docs/API.md)
- [Godbolt HLSL编译器页面](https://godbolt.org/) 